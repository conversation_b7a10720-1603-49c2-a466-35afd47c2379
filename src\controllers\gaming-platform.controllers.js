// MongoDb Models
import { User } from "../models/User.js";
import { GP_Game } from "../models/GP_Game.js";
import { GP_UserStats } from "../models/GP_UserStats.js";
import { GP_Chat } from "../models/GP_Forum.js";
import { ExternalToken } from "../models/ExternalToken.js";

// utils
import { generateController } from "../utils/generateController.js";
import { generateUserTokenBackend } from "../utils/helperFunctions.js";
import { generateExternalAccessToken } from "../utils/generateAccessToken.js";

//
import { ObjectId } from "mongodb";

// Get all games (no authentication required)
const getGames = generateController(
  async (request, response, raiseException) => {
    const games = await GP_Game.find(
      {},
      {
        _id: 1,
        game_id: 1,
        title: 1,
        description: 1,
        image: 1,
        blockchain: 1,
        category: 1,
        min_reward: 1,
        max_reward: 1,
        difficulty: 1,
        featured: 1,
        trending: 1,
      }
    );

    return {
      message: "Games fetched successfully",
      payload: { games },
    };
  }
);

// Get user stats for a specific game (with authentication)
const connectAccount = generateController(
  async (request, response, raiseException) => {
    const { token, tg_id } = request.body;
    if (!token || token.length !== 12) {
      return raiseException(400, "Invalid token format");
    }

    const userData = await User.findOne(
      { tg_id, is_deleted: false },
      {
        _id: 1,
        wallet_address: 1,
        in_game_name: 1,
        first_name: 1,
        last_name: 1,
        jargon_quest: 1,
        social_points: 1,
        is_admin: 1,
        is_moderator: 1,
      }
    );

    if (!userData) {
      return raiseException(404, "User not found");
    }

    const generatedHash = generateUserTokenBackend(
      userData._id.toString(),
      tg_id
    );

    if (generatedHash !== token) {
      return raiseException(400, "Invalid token");
    }

    const authToken = generateExternalAccessToken({
      userId: userData._id,
    });

    console.log("Received token:", token, "and tg_id:", tg_id);
    const isToken = await ExternalToken.create({
      user_id: userData._id,
      token: authToken,
    });

    if (!isToken) {
      return raiseException(500, "Token creation failed");
    }

    // Get user stats and last 4 records across all games using aggregation
    const [userStatsAggregation, last4RecordsAggregation] = await Promise.all([
      // Get basic game stats
      GP_UserStats.find({ user_id: userData._id }),

      // Get last 4 records across all games
      GP_UserStats.aggregate([
        {
          $match: { user_id: userData._id },
        },
        {
          $unwind: "$record",
        },
        {
          $addFields: {
            "record.game_id": "$game_id",
          },
        },
        {
          $sort: { "record.time_stamp": -1 },
        },
        {
          $limit: 4,
        },
        {
          $lookup: {
            from: GP_Game.collection.name, // Your games collection name
            localField: "game_id",
            foreignField: "game_id", // Assuming game_id references _id in games collection
            as: "gameData",
          },
        },
        {
          $project: {
            _id: 0,
            name: { $arrayElemAt: ["$gameData.title", 0] }, // Extract title from lookup result
            score: "$record.score",
            time_stamp: "$record.time_stamp",
          },
        },
      ]),
    ]);

    const gameStats = userStatsAggregation.reduce((acc, game) => {
      acc[game.game_id] = {
        best_score: game.best_score,
        games_played: game.games_played,
        total_score: game.total_score,
      };
      return acc;
    }, {});

    return {
      message: "Account connected successfully",
      payload: {
        user_data: userData,
        auth_token: authToken,
        game_stats: gameStats,
        last_4_played_games: last4RecordsAggregation,
      },
    };
  }
);

// Get user stats for a specific game (with authentication)
const connectAccountWithAuth = generateController(
  async (request, response, raiseException) => {
    const { userId, oldToken } = request.user;

    const userData = await User.findOne(
      { _id: userId, is_deleted: false },
      {
        _id: 1,
        wallet_address: 1,
        in_game_name: 1,
        first_name: 1,
        last_name: 1,
        jargon_quest: 1,
        social_points: 1,
      }
    );

    if (!userData) {
      return raiseException(404, "User not found");
    }

    const authToken = generateExternalAccessToken({
      userId: userData._id,
    });

    const isToken = await ExternalToken.updateOne(
      { user_id: userData._id, token: oldToken },
      { $set: { authToken } }
    ).exec();

    if (!isToken) {
      return raiseException(500, "Token creation failed");
    }

    // Get user stats and last 4 records across all games using aggregation
    const [userStatsAggregation, last4RecordsAggregation] = await Promise.all([
      // Get basic game stats
      GP_UserStats.find({ user_id: userData._id }),

      // Get last 4 records across all games
      GP_UserStats.aggregate([
        {
          $match: { user_id: userData._id },
        },
        {
          $unwind: "$record",
        },
        {
          $addFields: {
            "record.game_id": "$game_id",
          },
        },
        {
          $sort: { "record.time_stamp": -1 },
        },
        {
          $limit: 4,
        },
        {
          $lookup: {
            from: GP_Game.collection.name, // Your games collection name
            localField: "game_id",
            foreignField: "game_id", // Assuming game_id references _id in games collection
            as: "gameData",
          },
        },
        {
          $project: {
            _id: 0,
            name: { $arrayElemAt: ["$gameData.title", 0] }, // Extract title from lookup result
            score: "$record.score",
            time_stamp: "$record.time_stamp",
          },
        },
      ]),
    ]);

    const gameStats = userStatsAggregation.reduce((acc, game) => {
      acc[game.game_id] = {
        best_score: game.best_score,
        games_played: game.games_played,
        total_score: game.total_score,
      };
      return acc;
    }, {});

    return {
      message: "Account connected successfully",
      payload: {
        user_data: userData,
        auth_token: authToken,
        game_stats: gameStats,
        last_4_played_games: last4RecordsAggregation,
      },
    };
  }
);

// Update user score (with authentication)
const updateScore = generateController(
  async (request, response, raiseException) => {
    const { game_id, score } = request.body;
    const { userId } = request.user;

    const userData = await User.findOne(
      { _id: userId, is_deleted: false },
      {
        _id: 1,
      }
    );

    if (!userData) {
      return raiseException(404, "User not found");
    }

    // Find existing user stats or create new one
    let userStats = await GP_UserStats.findOne({
      user_id: userData._id,
      game_id: game_id,
    });

    if (!userStats) {
      // Create new stats record with initial record entry
      userStats = await GP_UserStats.create({
        user_id: userData._id,
        game_id: game_id,
        best_score: score,
        games_played: 1,
        total_score: score,
        record: [
          {
            time_stamp: new Date(),
            score: score,
          },
        ],
      });
    } else {
      // Update existing stats
      userStats.games_played += 1;
      userStats.total_score += score;

      // Update best score if current score is higher
      if (score > userStats.best_score) {
        userStats.best_score = score;
      }

      // Add new record entry and keep only last 4 records
      userStats.record.push({
        time_stamp: new Date(),
        score: score,
      });

      // Keep only the last 4 records
      if (userStats.record.length > 4) {
        userStats.record = userStats.record.slice(-4);
      }

      await userStats.save();
    }

    // Get last 4 records across all games after updating
    const last4RecordsAggregation = await GP_UserStats.aggregate([
      {
        $match: { user_id: userData._id },
      },
      {
        $unwind: "$record",
      },
      {
        $addFields: {
          "record.game_id": "$game_id",
        },
      },
      {
        $sort: { "record.time_stamp": -1 },
      },
      {
        $limit: 4,
      },
      {
        $project: {
          _id: 0,
          game_id: "$record.game_id",
          time_stamp: "$record.time_stamp",
          score: "$record.score",
        },
      },
    ]);

    return {
      message: "Score updated successfully",
      payload: {
        best_score: userStats.best_score,
        games_played: userStats.games_played,
        total_score: userStats.total_score,
        last_4_played_games: last4RecordsAggregation,
      },
    };
  }
);

// Get all channels (no authentication required)
const getChannels = generateController(
  async (request, response, raiseException) => {
    const channels = await GP_Chat.find(
      {},
      {
        _id: 1,
        channel_id: 1,
        channel_name: 1,
        channel_type: 1,
        member_count: 1,
        online_count: 1,
        only_admin: 1,
        only_moderators: 1,
        pinned_messages: 1,
      }
    );

    return {
      message: "Channels fetched successfully",
      payload: { channels },
    };
  }
);

// Get messages from a specific channel (with authentication)
const getChannelMessages = generateController(
  async (request, response, raiseException) => {
    const { channel_id } = request.params;
    const { page = 1, limit = 50 } = request.query;
    const { userId } = request.user;
    // console.log(user_id, channel_id, page, limit)

    // Verify user exists
    const userData = await User.findOne(
      { _id: userId, is_deleted: false },
      { _id: 1, in_game_name: 1, jargon_quest: 1 }
    );

    if (!userData) {
      return raiseException(404, "User not found");
    }

    // Find channel
    const channel = await GP_Chat.findOne({ channel_id });
    if (!channel) {
      return raiseException(404, "Channel not found");
    }

    // Calculate pagination
    const skip = (page - 1) * limit;
    const totalMessages = channel.messages.length;
    const messages = channel.messages
      .slice(-skip - limit, totalMessages - skip)
      .reverse();

    return {
      message: "Messages fetched successfully",
      payload: {
        channel_info: {
          channel_id: channel.channel_id,
          channel_name: channel.channel_name,
          channel_type: channel.channel_type,
          member_count: channel.member_count,
          online_count: channel.online_count,
          only_admin: channel.only_admin,
          only_moderators: channel.only_moderators,
        },
        messages,
        pagination: {
          currentPage: parseInt(page),
          totalMessages,
          hasMore: skip + limit < totalMessages,
        },
      },
    };
  }
);

// Send message to channel (with authentication)
const sendMessage = generateController(
  async (request, response, raiseException) => {
    const { channel_id, content, messageType = "text", replyTo } = request.body;
    const { userId } = request.user;
    console.log(userId);

    if (!content || content.trim().length === 0) {
      return raiseException(400, "Message content cannot be empty");
    }

    // Get user data with permissions
    const userData = await User.findOne(
      { _id: userId, is_deleted: false },
      {
        _id: 1,
        tg_id: 1,
        in_game_name: 1,
        first_name: 1,
        last_name: 1,
        jargon_quest: 1,
        // Assuming these fields exist for admin/moderator status
        is_admin: 1,
        is_moderator: 1,
      }
    );

    if (!userData) {
      return raiseException(404, "User not found");
    }

    // Find channel
    const channel = await GP_Chat.findOne({ channel_id });
    if (!channel) {
      return raiseException(404, "Channel not found");
    }

    // Create user object for message
    const userForMessage = {
      user_id: userData._id.toString(),
      username: userData.in_game_name || userData.first_name || "Anonymous",
      level: userData.jargon_quest?.level || 1,
      avatar: userData.jargon_quest?.avatar || "",
      is_admin: userData.is_admin || false,
      is_moderator: userData.is_moderator || false,
    };
    console.log(userForMessage);
    // Check permissions using the model method
    if (!channel.canUserSendMessage(userForMessage)) {
      return raiseException(
        403,
        "You don't have permission to send messages in this channel"
      );
    }

    // Add message using model method
    const newMessage = channel.addMessage(
      {
        content: content.trim(),
        messageType,
        replyTo,
      },
      userForMessage
    );

    await channel.save();

    return {
      message: "Message sent successfully",
      payload: {
        message: newMessage,
        channel_id: channel.channel_id,
      },
    };
  }
);

// Add reaction to message (with authentication)
const addReaction = generateController(
  async (request, response, raiseException) => {
    const { channel_id, message_id, emoji } = request.body;
    const { userId } = request.user;

    // Verify user exists
    const userData = await User.findOne(
      { _id: userId, is_deleted: false },
      { _id: 1, in_game_name: 1 }
    );

    if (!userData) {
      return raiseException(404, "User not found");
    }

    // Find channel and message
    const channel = await GP_Chat.findOne({ channel_id });
    if (!channel) {
      return raiseException(404, "Channel not found");
    }

    const message = channel.messages.find(
      (msg) => msg.message_id === message_id
    );
    if (!message) {
      return raiseException(404, "Message not found");
    }

    // Check if user already reacted with this emoji
    const existingReaction = message.reactions.find((r) => r.emoji === emoji);
    const userIdStr = userId.toString();

    if (existingReaction) {
      if (existingReaction.users.includes(userIdStr)) {
        // Remove reaction
        existingReaction.users = existingReaction.users.filter(
          (id) => id !== userIdStr
        );
        existingReaction.count = existingReaction.users.length;

        // Remove reaction if no users left
        if (existingReaction.count === 0) {
          message.reactions = message.reactions.filter(
            (r) => r.emoji !== emoji
          );
        }
      } else {
        // Add user to existing reaction
        existingReaction.users.push(userIdStr);
        existingReaction.count = existingReaction.users.length;
      }
    } else {
      // Create new reaction
      message.reactions.push({
        emoji,
        count: 1,
        users: [userIdStr],
      });
    }

    await channel.save();

    return {
      message: "Reaction updated successfully",
      payload: {
        message_id,
        reactions: message.reactions,
      },
    };
  }
);

// Delete message (admin/moderator or message author)
const deleteMessage = generateController(
  async (request, response, raiseException) => {
    const { channel_id, message_id } = request.params;
    const { userId } = request.user;

    // Get user data with permissions
    const userData = await User.findOne(
      { _id: userId, is_deleted: false },
      { _id: 1, is_admin: 1, is_moderator: 1 }
    );

    if (!userData) {
      return raiseException(404, "User not found");
    }

    // Find channel
    const channel = await GP_Chat.findOne({ channel_id });
    if (!channel) {
      return raiseException(404, "Channel not found");
    }

    const messageIndex = channel.messages.findIndex(
      (msg) => msg.message_id === message_id
    );
    if (messageIndex === -1) {
      return raiseException(404, "Message not found");
    }

    const message = channel.messages[messageIndex];

    // Check if user can delete (admin, moderator, or message author)
    const canDelete =
      userData.is_admin ||
      userData.is_moderator ||
      message.sender.userId === userId.toString();

    if (!canDelete) {
      return raiseException(
        403,
        "You don't have permission to delete this message"
      );
    }

    // Remove message from array
    channel.messages.splice(messageIndex, 1);
    await channel.save();

    return {
      message: "Message deleted successfully",
      payload: {
        message_id,
        channel_id,
      },
    };
  }
);

export {
  getGames,
  connectAccount,
  connectAccountWithAuth,
  updateScore,
  getChannels,
  getChannelMessages,
  sendMessage,
  addReaction,
  deleteMessage,
};
