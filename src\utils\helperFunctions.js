// Backend Implementation
import { ethers } from "ethers";

const SECRET = process.env.HASHING_TOKEN_SECRET;

// Generate token (same logic as frontend)
export const generateUserTokenBackend = (userId, tgId) => {
  try {
    const payload = {
      uid: userId.substring(userId.length - 8),
      tgid: tgId,
      version: 1,
    };

    const payloadString = JSON.stringify(payload);
    const signatureData = `${payloadString}:${SECRET}`;
    const signature = ethers
      .keccak256(ethers.toUtf8Bytes(signatureData))
      .substring(2, 16);
    const combined = `${payloadString}:${signature}`;
    const finalToken = ethers
      .keccak256(ethers.toUtf8Bytes(combined))
      .substring(2, 14);
    console.log("Final Token:", finalToken);
    return finalToken.toUpperCase();
  } catch (error) {
    console.log("Error generating token:", error);
    return null;
  }
};
