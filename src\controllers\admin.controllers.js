// MongoDb Models
import { User } from "../models/User.js";
import { Card } from "../models/Card.js";
import { GP_Chat } from "../models/GP_Forum.js";
import { AffiliatedProduct } from "../models/AffiliateProducts.js";

// utils
import { generateController } from "../utils/generateController.js";
import { blockchainsData, exchangesData } from "../utils/adminFunctions.js";
import {
  buildMatchFromFilters,
  parseFiltersFromQuery,
} from "../utils/filters.js";
import { decodeId } from "../utils/urlDecoder.js";

const getDashboardData = generateController(
  async (request, response, raiseException) => {
    const users = await User.aggregate([
      {
        $facet: {
          totalUsers: [{ $count: "total" }],
          binanceUsers: [
            { $match: { exchange: "binance" } },
            { $count: "binanceCount" },
          ],
          kucoinUsers: [
            { $match: { exchange: "kucoin" } },
            { $count: "kucoinCount" },
          ],
          okxUsers: [{ $match: { exchange: "okx" } }, { $count: "okxCount" }],
        },
      },
      {
        $project: {
          data: {
            $concatArrays: [
              [
                {
                  usersAmount: {
                    $ifNull: [{ $arrayElemAt: ["$totalUsers.total", 0] }, 0],
                  },
                  isExchange: false,
                  title: "Total Users",
                },
              ],
              [
                {
                  usersAmount: {
                    $ifNull: [
                      { $arrayElemAt: ["$binanceUsers.binanceCount", 0] },
                      0,
                    ],
                  },
                  isExchange: true,
                  title: "Binance Users",
                },
              ],
              [
                {
                  usersAmount: {
                    $ifNull: [
                      { $arrayElemAt: ["$kucoinUsers.kucoinCount", 0] },
                      0,
                    ],
                  },
                  isExchange: true,
                  title: "Kucoin Users",
                },
              ],
              [
                {
                  usersAmount: {
                    $ifNull: [{ $arrayElemAt: ["$okxUsers.okxCount", 0] }, 0],
                  },
                  isExchange: true,
                  title: "OKX Users",
                },
              ],
            ],
          },
        },
      },
      { $unwind: "$data" },
      { $replaceRoot: { newRoot: "$data" } },
    ]);

    if (!users) {
      return raiseException(500, "An error occurred fetching dashboard data");
    }

    const usersWithExchangesData = await exchangesData();

    if (!usersWithExchangesData) {
      return raiseException(500, "An error occurred fetching dashboard data");
    }

    const usersWithBlockchainsData = await blockchainsData();

    if (!usersWithBlockchainsData) {
      return raiseException(500, "An error occurred fetching dashboard data");
    }

    return {
      message: "Dashboard data fetched successfully",
      payload: {
        users,
        users_with_exchanges_data: usersWithExchangesData,
        users_with_blockchains_data: usersWithBlockchainsData,
      },
    };
  }
);

const getUsersData = generateController(
  async (request, response, raiseException) => {
    const { page_number, page_length, filters } = request.query;
    const mineCards = await Card.find().exec();

    if (!mineCards) {
      return raiseException(
        500,
        "An error occurred while fetching invited friends"
      );
    }
    //   {
    //     $match: { is_deleted: false },
    //   },
    //   {
    //     $addFields: {
    //       cards_levels: {
    //         $cond: {
    //           if: { $eq: [{ $type: "$cards_levels" }, "missing"] }, // Check if cards_levels is missing
    //           then: [], // If missing, use an empty array
    //           else: {
    //             $map: {
    //               input: { $objectToArray: "$cards_levels" }, // Convert cards_levels to an array of key-value pairs
    //               as: "role",
    //               in: {
    //                 k: "$$role.k", // Keep the original key
    //                 v: {
    //                   $mergeObjects: [
    //                     {
    //                       pph: {
    //                         $let: {
    //                           vars: {
    //                             // Find the corresponding card from mineCards by matching the id
    //                             card: {
    //                               $first: {
    //                                 $filter: {
    //                                   input: mineCards,
    //                                   as: "card",
    //                                   cond: {
    //                                     $eq: ["$$card._id", "$$role.k"],
    //                                   },
    //                                 },
    //                               },
    //                             },
    //                           },
    //                           in: {
    //                             $multiply: [
    //                               "$$card.base_pph",
    //                               {
    //                                 $divide: [
    //                                   {
    //                                     $subtract: [
    //                                       {
    //                                         $pow: [
    //                                           "$$card.profit_increase_rate",
    //                                           "$$role.v",
    //                                         ],
    //                                       },
    //                                       1,
    //                                     ],
    //                                   },
    //                                   {
    //                                     $subtract: [
    //                                       "$$card.profit_increase_rate",
    //                                       1,
    //                                     ],
    //                                   },
    //                                 ],
    //                               },
    //                             ],
    //                           },
    //                         },
    //                       },
    //                     },
    //                   ],
    //                 },
    //               },
    //             },
    //           },
    //         },
    //       },
    //     },
    //   },
    //   {
    //     $addFields: {
    //       cards_levels: { $arrayToObject: "$cards_levels" }, // Convert back to object
    //     },
    //   },
    //   // Sum the `pph` values or set total_pph to 0 if there are no cards_levels
    //   {
    //     $addFields: {
    //       total_pph: {
    //         $cond: {
    //           if: {
    //             $gt: [{ $size: { $objectToArray: "$cards_levels" } }, 0],
    //           },
    //           then: {
    //             $reduce: {
    //               input: { $objectToArray: "$cards_levels" },
    //               initialValue: 0,
    //               in: { $add: ["$$value", "$$this.v.pph"] },
    //             },
    //           },
    //           else: 0,
    //         },
    //       },
    //     },
    //   },
    //   {
    //     $project: {
    //       _id: 0,
    //       id: "$_id",
    //       tg_id: 1,
    //       first_name: 1,
    //       last_name: {
    //         $ifNull: ["$last_name", "-"],
    //       },
    //       username: {
    //         $ifNull: ["$username", "-"],
    //       },
    //       is_premium: 1,
    //       total_points: { $floor: "$total_points" },
    //       wallet_address: {
    //         $ifNull: ["$wallet_address", "-"],
    //       },
    //       in_game_name: {
    //         $ifNull: [
    //           "$in_game_name",
    //           { $concat: ["$first_name", " ", "$last_name"] },
    //         ],
    //       },
    //       exchange: {
    //         $ifNull: ["$exchange", "-"],
    //       },
    //       country: {
    //         $ifNull: ["$country", "-"],
    //       },
    //       total_pph: { $floor: "$total_pph" },
    //       referrals_count: { $size: { $ifNull: ["$referrals", []] } }, // Calculate the length of the referrals array
    //     },
    //   },
    // ]).exec();

    const page = parseInt(page_number || 0);
    const length = parseInt(page_length || 100);
    const skip = page * length;

    const filtersArray = parseFiltersFromQuery(filters);
    const matchStage = { $match: buildMatchFromFilters(filtersArray) };

    const users = await User.aggregate([
      matchStage,
      { $sort: { _id: 1 } }, // optional, add meaningful sorting if needed
      { $skip: skip },
      { $limit: length },
      {
        $addFields: {
          cards_data: {
            $cond: {
              if: { $eq: [{ $type: "$cards_data" }, "missing"] },
              then: [],
              else: {
                $map: {
                  input: { $objectToArray: "$cards_data" },
                  as: "role",
                  in: {
                    k: "$$role.k",
                    v: {
                      $mergeObjects: [
                        {
                          pph: {
                            $let: {
                              vars: {
                                card: {
                                  $first: {
                                    $filter: {
                                      input: mineCards,
                                      as: "card",
                                      cond: {
                                        $eq: ["$$card._id", "$$role.k"],
                                      },
                                    },
                                  },
                                },
                              },
                              in: {
                                $multiply: [
                                  "$$card.base_pph",
                                  {
                                    $divide: [
                                      {
                                        $subtract: [
                                          {
                                            $pow: [
                                              "$$card.profit_increase_rate",
                                              "$$role.v.level",
                                            ],
                                          },
                                          1,
                                        ],
                                      },
                                      {
                                        $subtract: [
                                          "$$card.profit_increase_rate",
                                          1,
                                        ],
                                      },
                                    ],
                                  },
                                ],
                              },
                            },
                          },
                        },
                      ],
                    },
                  },
                },
              },
            },
          },
        },
      },
      {
        $addFields: {
          cards_data: { $arrayToObject: "$cards_data" },
        },
      },
      {
        $addFields: {
          total_pph: {
            $cond: {
              if: {
                $gt: [{ $size: { $objectToArray: "$cards_data" } }, 0],
              },
              then: {
                $reduce: {
                  input: { $objectToArray: "$cards_data" },
                  initialValue: 0,
                  in: { $add: ["$$value", "$$this.v.pph"] },
                },
              },
              else: 0,
            },
          },
        },
      },
      {
        $lookup: {
          from: User.collection.name,
          localField: "referrals.tg_id", // Adjusted to match the new schema
          foreignField: "tg_id",
          as: "referrals_with_pph",
          pipeline: [
            {
              $addFields: {
                cards_data: {
                  $cond: {
                    if: { $eq: [{ $type: "$cards_data" }, "missing"] },
                    then: [],
                    else: {
                      $map: {
                        input: { $objectToArray: "$cards_data" },
                        as: "role",
                        in: {
                          k: "$$role.k",
                          v: {
                            $mergeObjects: [
                              {
                                pph: {
                                  $let: {
                                    vars: {
                                      card: {
                                        $first: {
                                          $filter: {
                                            input: mineCards,
                                            as: "card",
                                            cond: {
                                              $eq: ["$$card._id", "$$role.k"],
                                            },
                                          },
                                        },
                                      },
                                    },
                                    in: {
                                      $multiply: [
                                        "$$card.base_pph",
                                        {
                                          $divide: [
                                            {
                                              $subtract: [
                                                {
                                                  $pow: [
                                                    "$$card.profit_increase_rate",
                                                    "$$role.v.level",
                                                  ],
                                                },
                                                1,
                                              ],
                                            },
                                            {
                                              $subtract: [
                                                "$$card.profit_increase_rate",
                                                1,
                                              ],
                                            },
                                          ],
                                        },
                                      ],
                                    },
                                  },
                                },
                              },
                            ],
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
            {
              $addFields: {
                cards_data: { $arrayToObject: "$cards_data" },
              },
            },
            {
              $addFields: {
                total_pph: {
                  $cond: {
                    if: {
                      $gt: [{ $size: { $objectToArray: "$cards_data" } }, 0],
                    },
                    then: {
                      $reduce: {
                        input: { $objectToArray: "$cards_data" },
                        initialValue: 0,
                        in: { $add: ["$$value", "$$this.v.pph"] },
                      },
                    },
                    else: 0,
                  },
                },
              },
            },
            {
              $project: {
                _id: 0,
                total_pph: 1,
                referrals: 1,
              },
            },
          ],
        },
      },
      {
        $addFields: {
          referrals_active: {
            $size: {
              $filter: {
                input: "$referrals_with_pph",
                as: "referral",
                cond: {
                  $and: [
                    { $gte: ["$$referral.total_pph", 10000] }, // Calculate if referral's pph is >= 10,000
                    { $gt: [{ $size: "$$referral.referrals" }, 0] }, // Check if referral has at least one referral
                  ],
                },
              },
            },
          },
          referrals_total: { $size: "$referrals" }, // Total referrals
        },
      },
      {
        $addFields: {
          referrals_count: {
            $concat: [
              { $toString: "$referrals_active" },
              "/",
              {
                $toString: {
                  $subtract: ["$referrals_total", "$referrals_active"],
                },
              },
            ],
          },
        },
      },
      {
        $addFields: {
          status: {
            $cond: {
              if: {
                $and: [
                  { $gte: ["$total_pph", 10000] }, // Check if user's total_pph is >= 10,000
                  { $gte: ["$referrals_total", 1] }, // Check if user has at least one referral
                ],
              },
              then: "active",
              else: "in-active",
            },
          },
        },
      },
      {
        $project: {
          _id: 0,
          id: "$_id",
          tg_id: 1,
          first_name: 1,
          last_name: { $ifNull: ["$last_name", "-"] },
          username: { $ifNull: ["$username", "-"] },
          status: 1,
          is_premium: 1,
          jargon_quest: { $ifNull: ["$jargon_quest", 0] },
          claimed_jargon_quest: { $ifNull: ["$claimed_jargon_quest", 0] },
          total_points: { $floor: "$total_points" },
          wallet_address: { $ifNull: ["$wallet_address", "-"] },
          in_game_name: {
            $ifNull: [
              "$in_game_name",
              { $concat: ["$first_name", " ", "$last_name"] },
            ],
          },
          exchange: { $ifNull: ["$exchange", "-"] },
          country: { $ifNull: ["$country", "-"] },
          total_pph: { $floor: "$total_pph" },
          referrals_count: 1,
        },
      },
    ]).exec();

    if (!users) {
      return raiseException(500, "An error occurred fetching dashboard data");
    }

    const result = await User.aggregate([
      matchStage,
      { $count: "data_count" },
    ]).exec();

    const count = result[0]?.data_count || 0;

    return {
      message: "Users data fetched successfully",
      payload: { users, count },
    };
  }
);

const getExchangesData = generateController(
  async (request, response, raiseException) => {
    const usersWithExchangesData = await exchangesData();

    if (!usersWithExchangesData) {
      return raiseException(500, "An error occurred fetching dashboard data");
    }

    return {
      message: "Exchanges data fetched successfully",
      payload: { users_with_exchanges_data: usersWithExchangesData },
    };
  }
);

const getBlockchainsData = generateController(
  async (request, response, raiseException) => {
    const usersWithBlockchainsData = await blockchainsData();

    if (!usersWithBlockchainsData) {
      return raiseException(500, "An error occurred fetching dashboard data");
    }
    return {
      message: "Blockchains data fetched successfully",
      payload: { users_with_blockchains_data: usersWithBlockchainsData },
    };
  }
);

const addCards = generateController(
  async (request, response, raiseException) => {
    const data = request.body;

    const cardData = await Card.create(data);

    if (!cardData) {
      return raiseException(500, "An error occurred while adding card");
    }

    return {
      message: "Card added successfully",
    };
  }
);

const getCards = generateController(
  async (request, response, raiseException) => {
    const cards = await Card.aggregate([
      {
        $match: { is_deleted: false },
      },
      {
        $project: {
          _id: 0,
          id: "$_id",
          deck: 1,
          name: 1,
          description: 1,
          base_price: 1,
          base_pph: 1,
          max_level: 1,
          cool_down_period: 1,
          card_availability: 1,
          cost_increase_rate: { $multiply: ["$cost_increase_rate", 100] },
          profit_increase_rate: { $multiply: ["$profit_increase_rate", 100] },
          affiliated_link_url: { $ifNull: ["$affiliated_link.url", "-"] },
          affiliated_link_text: { $ifNull: ["$affiliated_link.text", "-"] },
          image: { $ifNull: ["$image.src", "-"] },
          locked_state: { $ifNull: ["$locked_state.admin_text", "-"] },
          shareable: { $ifNull: ["$shareable", false] },
        },
      },
    ]).exec();

    if (!cards) {
      return raiseException(500, "An error occurred while fetching cards");
    }

    return {
      message: "Cards fetched successfully",
      payload: { cards },
    };
  }
);

const getSingleCard = generateController(
  async (request, response, raiseException) => {
    const { id } = request.params;
    const decodedId = decodeId(id);

    const cardData = await Card.findOne(
      { _id: decodedId, is_deleted: false },
      { is_deleted: 0 }
    ).exec();

    if (!cardData) {
      return raiseException(500, "An error occurred while fetching cards");
    }

    return {
      message: "Cards fetched successfully",
      payload: { card: cardData },
    };
  }
);

const getSingleUser = generateController(
  async (request, response, raiseException) => {
    const { id } = request.params;
    const decodedId = decodeId(id);

    const userData = await User.findOne(
      { _id: decodedId, is_deleted: false },
      { is_deleted: 0 }
    ).exec();

    if (!userData) {
      return raiseException(500, "An error occurred while fetching user");
    }

    return {
      message: "User fetched successfully",
      payload: { user: userData },
    };
  }
);

const updateCard = generateController(
  async (request, response, raiseException) => {
    const { id } = request.params;
    const decodedId = decodeId(id);
    const { updateFields, unsetFields } = request.body;

    const cardData = await Card.findOneAndUpdate(
      { _id: decodedId, is_deleted: false },
      {
        $set: updateFields,
        $unset: unsetFields,
      }
    ).exec();

    if (!cardData) {
      return raiseException(500, "An error occurred while updating card");
    }

    return {
      message: "Card updated successfully",
    };
  }
);

const updateUser = generateController(
  async (request, response, raiseException) => {
    const { id } = request.params;
    const decodedId = decodeId(id);
    const data = request.body;

    const userData = await User.findOneAndUpdate(
      { _id: decodedId, is_deleted: false },
      data
    ).exec();

    if (!userData) {
      return raiseException(500, "An error occurred while updating user");
    }

    return {
      message: "User updated successfully",
    };
  }
);

const deleteCard = generateController(
  async (request, response, raiseException) => {
    const { id } = request.params;
    const decodedId = decodeId(id);

    const cardData = await Card.findOneAndUpdate(
      { _id: decodedId, is_deleted: false },
      {
        is_deleted: true,
      }
    ).exec();

    if (!cardData) {
      return raiseException(500, "An error occurred while deleting card");
    }

    return {
      message: "Card deleted successfully",
    };
  }
);

const addAffiliatedProduct = generateController(
  async (request, response, raiseException) => {
    const data = request.body;

    const productData = await AffiliatedProduct.create(data);

    if (!productData) {
      return raiseException(
        500,
        "An error occurred while adding Affiliated Product"
      );
    }

    return {
      message: "Affiliated Product added successfully",
    };
  }
);

const getAffiliatedProducts = generateController(
  async (request, response, raiseException) => {
    const AffiliatedProducts = await AffiliatedProduct.aggregate([
      {
        $match: { is_deleted: false },
      },
      {
        $project: {
          _id: 0,
          id: "$_id",
          name: 1,
          availability: 1,
          affiliated_link: 1,
          total_clicks: 1,
          image: { $ifNull: ["$image.src", "-"] },
        },
      },
    ]).exec();

    if (!AffiliatedProducts) {
      return raiseException(500, "An error occurred while Affiliated Products");
    }

    return {
      message: "Affiliated Products fetched successfully",
      payload: { AffiliatedProducts },
    };
  }
);

const getAffiliatedProduct = generateController(
  async (request, response, raiseException) => {
    const { id } = request.params;
    const decodedId = decodeId(id);

    const productData = await AffiliatedProduct.findOne(
      { _id: decodedId, is_deleted: false },
      { is_deleted: 0 }
    ).exec();

    if (!productData) {
      return raiseException(500, "An error occurred while Affiliated Product");
    }

    return {
      message: "Affiliated Product fetched successfully",
      payload: { product: productData },
    };
  }
);

const updateAffiliatedProduct = generateController(
  async (request, response, raiseException) => {
    const { id } = request.params;
    const decodedId = decodeId(id);
    const data = request.body;

    const productData = await AffiliatedProduct.findOneAndUpdate(
      { _id: decodedId, is_deleted: false },
      {
        $set: data,
      }
    ).exec();

    if (!productData) {
      return raiseException(
        500,
        "An error occurred while updating affiliated product"
      );
    }

    return {
      message: "Affiliated product updated successfully",
    };
  }
);

const deleteAffiliatedProduct = generateController(
  async (request, response, raiseException) => {
    const { id } = request.params;
    const decodedId = decodeId(id);

    const productData = await AffiliatedProduct.findOneAndUpdate(
      { _id: decodedId, is_deleted: false },
      {
        is_deleted: true,
      }
    ).exec();

    if (!productData) {
      return raiseException(
        500,
        "An error occurred while deleting affiliated product"
      );
    }

    return {
      message: "Affiliated product deleted successfully",
    };
  }
);

const createChannel = generateController(
  async (request, response, raiseException) => {
    const data = request.body;

    // Check if channel already exists
    const existingChannel = await GP_Chat.findOne({ channel_id: data.channel_id });
    console.log(existingChannel)
    if (existingChannel) {
      return raiseException(400, "Channel with this name already exists");
    }

    // Create new channel
    const newChannel = await GP_Chat.create({
      ...data,
      member_count: 1, // Creator is first member
      messages: []
    });

    if (!newChannel) {
      return raiseException(
        500,
        "An error occurred while creating channel"
      );
    }

    return {
      message: "Channel created successfully",
    };
  }
);

// Update channel settings (admin only)
// const updateChannelSettings = generateController(
//   async (request, response, raiseException) => {
//     const { channelId } = request.params;
//     const {
//       description,
//       onlyAdmin,
//       onlyModerators,
//       readOnly,
//       slowMode,
//       nsfw
//     } = request.body.data;
//     // Find and update channel
//     const updateData = {};
//     if (description !== undefined) updateData.description = description;
//     if (onlyAdmin !== undefined) updateData.onlyAdmin = onlyAdmin;
//     if (onlyModerators !== undefined) updateData.onlyModerators = onlyModerators;
//     if (readOnly !== undefined) updateData.readOnly = readOnly;
//     if (slowMode !== undefined) updateData.slowMode = slowMode;
//     if (nsfw !== undefined) updateData.nsfw = nsfw;

//     const channel = await GP_Chat.findOneAndUpdate(
//       { channelId },
//       { $set: updateData },
//       { new: true }
//     );

//     if (!channel) {
//       return raiseException(404, "Channel not found");
//     }

//     // raiseException(500, "Failed to update channel settings: " + error.message);
//     return {
//       message: "Channel settings updated successfully",
//     };
//   }
// );

export {
  getDashboardData,
  getUsersData,
  getExchangesData,
  getBlockchainsData,
  addCards,
  getCards,
  getSingleCard,
  getSingleUser,
  updateCard,
  updateUser,
  deleteCard,
  addAffiliatedProduct,
  getAffiliatedProducts,
  getAffiliatedProduct,
  updateAffiliatedProduct,
  deleteAffiliatedProduct,
  createChannel,
  // updateChannelSettings,
};
