import mongoose from "mongoose";

const gp_gameSchema = new mongoose.Schema({
  game_id: {
    type: String,
    required: true,
    unique: true,
  },
  title: {
    type: String,
    required: true,
  },
  description: {
    type: String,
    required: true,
  },
  image: {
    type: String,
    required: true,
  },
  blockchain: {
    type: String,
    required: true,
  },
  category: {
    type: String,
    required: true,
  },
  min_reward: {
    type: Number,
    required: true,
  },
  max_reward: {
    type: Number,
    required: true,
  },
  difficulty: {
    type: String,
    enum: ["Easy", "Medium", "Hard"],
    required: true,
  },
  featured: {
    type: Boolean,
    required: true,
    default: true,
  },
  trending: {
    type: Boolean,
    required: true,
    default: true,
  },
});

export const GP_Game = mongoose.model("GP_Game", gp_gameSchema);
