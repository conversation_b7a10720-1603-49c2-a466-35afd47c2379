import joi from "joi";

const getUserStatsSchema = joi.object().keys({
  game_id: joi.string().required(),
  tg_id: joi.number().required(),
});

const connectAccountSchema = joi.object({
  token: joi.string().required(),
  tg_id: joi.number().required(),
});

const updateScoreSchema = joi.object().keys({
  game_id: joi.string().required(),
  score: joi.number().min(0).required(),
});

// Schema for sending messages
const sendMessageSchema = joi.object({
  channel_id: joi.string().required().min(1).max(50),
  content: joi.string().required().min(1).max(2000).trim(),
  messageType: joi.string().valid("text", "image", "file", "system").default("text"),
  replyTo: joi.string().optional().allow(null)
});

// Schema for adding reactions
const addReactionSchema = joi.object({
  channel_id: joi.string().required().min(1).max(50),
  message_id: joi.string().required(),
  emoji: joi.string().required().min(1).max(10)
});

export { getUserStatsSchema, connectAccountSchema, updateScoreSchema, sendMessageSchema, addReactionSchema };
