import mongoose from "mongoose";

const gp_userStatsSchema = new mongoose.Schema({
  user_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: true,
  },
  game_id: {
    type: String,
    required: true,
  },
  best_score: {
    type: Number,
    required: true,
    default: 0,
  },
  games_played: {
    type: Number,
    required: true,
    default: 0,
  },
  total_score: {
    type: Number,
    required: true,
    default: 0,
  },
  record: {
    type: [
      {
        time_stamp: { type: Date, required: true },
        score: { type: Number, required: true },
      },
    ],
    validate: (v) => Array.isArray(v) && v.length > 0,
  },
});

export const GP_UserStats = mongoose.model("GP_UserStats", gp_userStatsSchema);
