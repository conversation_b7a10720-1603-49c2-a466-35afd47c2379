import express from "express";
import { validateBody } from "../middlewares/validateBody.js";
import { apiKeyAuth, externalUserAuth } from "../middlewares/authentication.js";
import {
  connectAccountSchema,
  getUserStatsSchema,
  updateScoreSchema,
  sendMessageSchema,
  addReactionSchema,
} from "../schemaValidation/gp_game.schema.js";
import {
  getGames,
  connectAccount,
  connectAccountWithAuth,
  updateScore,
  getChannels,
  getChannelMessages,
  sendMessage,
  addReaction,
  deleteMessage,
} from "../controllers/gaming-platform.controllers.js";

const router = express.Router();

router.get("/games", apiKeyAuth, getGames);
router.post(
  "/connect-account",
  apiKeyAuth,
  validateBody(connectAccountSchema),
  connectAccount
);
router.post(
  "/connect-account-with-auth",
  apiKeyAuth,
  externalUserAuth,
  connectAccountWithAuth
);
router.patch(
  "/update-score",
  api<PERSON>ey<PERSON>uth,
  externalUserAuth,
  validateBody(updateScoreSchema),
  updateScore
);
// Get all channels (public with API key)
router.get("/channels", apiKeyAuth, getChannels);

// Get messages from specific channel (authenticated)
router.get(
  "/channels/:channel_id/messages",
  apiKeyAuth,
  externalUserAuth,
  getChannelMessages
);

// Send message to channel (authenticated with validation)
router.post(
  "/channels/:channel_id/messages",
  apiKeyAuth,
  externalUserAuth,
  validateBody(sendMessageSchema),
  sendMessage
);

// Add/remove reaction to message (authenticated with validation)
router.post(
  "/reactions",
  apiKeyAuth,
  externalUserAuth,
  validateBody(addReactionSchema),
  addReaction
);

// Delete message (authenticated)
router.delete(
  "/channels/:channelId/messages/:messageId",
  apiKeyAuth,
  externalUserAuth,
  deleteMessage
);

export default router;
