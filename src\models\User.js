import mongoose from "mongoose";

const userSchema = new mongoose.Schema({
  tg_id: {
    type: Number,
    unique: true,
    required: true,
  },
  first_name: {
    type: String,
    required: true,
  },
  last_name: {
    type: String,
  },
  username: {
    type: String,
  },
  is_premium: {
    type: Boolean,
    required: true,
    default: false,
  },
  total_points: {
    type: Number,
    required: true,
  },
  jargon_quest: {
    type: Number,
    required: true,
    default: 0,
  },
  claimed_jargon_quest: {
    type: Number,
    required: true,
    default: 0,
  },
  wallet_address: {
    type: String,
  },
  in_game_name: {
    type: String,
  },
  exchange: {
    type: String,
    enum: [
      "binance",
      "okx",
      "crypto.com",
      "bybit",
      "bingX",
      "htx",
      "kucoin",
      "gate.io",
      "mexc",
      "bitget",
    ],
  },
  country: {
    type: String,
  },
  referrals: [
    {
      tg_id: {
        type: Number,
        required: true,
      },
      retrieved_jq_from_ref: {
        type: Boolean,
        required: true,
        default: false,
      },
    },
  ],
  social_points: {
    type: Number,
  },
  frozen_social_points: {
    type: Number,
    default: 0,
  },
  affiliate_product_interactions: {
    type: mongoose.Schema.Types.Mixed,
  },
  social_card_interactions: {
    type: mongoose.Schema.Types.Mixed,
  },
  // to be removed when migration completed
  cards_levels: {
    type: mongoose.Schema.Types.Mixed,
  },
  cards_data: {
    type: mongoose.Schema.Types.Mixed,
  },
  start_time: {
    type: Date,
  },
  close_time: {
    type: Date,
  },
  haptic_feedback: {
    type: Boolean,
    required: true,
    default: false,
  },
  is_deleted: {
    type: Boolean,
    required: true,
    default: false,
  },
  availability_status: {
    type: String,
    enum: ["online", "offline"],
    required: true,
    default: "online",
  },
  //   isAdmin: { type: Boolean, default: false },
  //   isModerator: { type: Boolean, default: false },
});

export const User = mongoose.model("User", userSchema);
