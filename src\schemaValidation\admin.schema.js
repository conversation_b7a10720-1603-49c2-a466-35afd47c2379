import joi from "joi";

const addCardSchema = joi.object({
  _id: joi.string().required(),
  deck: joi
    .string()
    .valid("company", "blockchain", "psi_book", "news", "special")
    .required(),
  name: joi.string().required(),
  description: joi.string(),
  affiliated_link: joi.object({
    url: joi.string().uri().required(),
    text: joi.string().allow(""),
  }),
  base_price: joi.number().required(),
  base_pph: joi.number().required(),
  max_level: joi.number(),
  cool_down_period: joi.number(),
  card_availability: joi.string().isoDate(),
  cost_increase_rate: joi.number().required(),
  profit_increase_rate: joi.number().required(),
  image: joi.object({
    src: joi.string().uri().required(),
    alt: joi.string().required(),
  }),
  shareable: joi.boolean().default(false),
  locked_state: joi.object({
    friends_value: joi.number().greater(0).required(),
    admin_text: joi.string().required(),
    message: joi.string(),
  }),
});

const addAffiliatedProductSchema = joi.object({
  _id: joi.string().required(),
  name: joi.string().required(),
  availability: joi.string().isoDate().required(),
  affiliated_link: joi.string().required(),
  image: joi.object({
    src: joi.string().uri().required(),
    alt: joi.string().required(),
  }),
});

const updateAffiliatedProductSchema = joi.object({
  name: joi.string(),
  availability: joi.string().isoDate(),
  affiliated_link: joi.string(),
  image: joi.object({
    src: joi.string().uri(),
    alt: joi.string(),
  }),
});

const updateCardSchema = joi.object({
  updateFields: joi.object({
    deck: joi
      .string()
      .valid("company", "blockchain", "psi_book", "news", "special"),
    name: joi.string(),
    description: joi.string(),
    affiliated_link: joi.object({
      url: joi.string().uri(),
      text: joi.string().allow(""),
    }),
    base_price: joi.number(),
    base_pph: joi.number(),
    max_level: joi.number(),
    cool_down_period: joi.number(),
    card_availability: joi.string().isoDate(),
    cost_increase_rate: joi.number(),
    profit_increase_rate: joi.number(),
    image: joi.object({
      src: joi.string().uri(),
      alt: joi.string(),
    }),
    shareable: joi.boolean().default(false),
    locked_state: joi.object({
      friends_value: joi.number().greater(0),
      admin_text: joi.string(),
      message: joi.string(),
    }),
  }),
  unsetFields: joi.object({
    shareable: joi.number(),
    description: joi.number(),
    cool_down_period: joi.number(),
    card_availability: joi.number(),
    affiliated_link: joi.number(),
    max_level: joi.number(),
    image: joi.number(),
    locked_state: joi.number(),
  }),
});

const updateUserSchema = joi
  .object({
    jargon_quest: joi.number(),
    social_points: joi.number(),
  })
  .or("jargon_quest", "social_points");

// Schema for creating channels
const createChannelSchema = joi.object({
  channel_id: joi.string().required().min(1).max(50),
  channel_name: joi.string().required().min(1).max(50).trim(),
  channel_type: joi.string().valid("text", "voice", "announcement", "gaming", "trading", "support").default("text"),
  only_admin: joi.boolean().default(false),
  only_moderators: joi.boolean().default(false)
});

// // Schema for updating channel settings
// const updateChannelSettingsSchema = joi.object({
//   only_admin: joi.boolean().optional(),
//   only_moderators: joi.boolean().optional(),
// });

export {
  addCardSchema,
  updateCardSchema,
  addAffiliatedProductSchema,
  updateAffiliatedProductSchema,
  updateUserSchema,
  createChannelSchema,
  // updateChannelSettingsSchema
};
