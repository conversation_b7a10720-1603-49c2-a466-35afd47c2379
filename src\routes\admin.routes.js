import express from "express";
import {
  getBlockchainsData,
  getUsersData,
  getDashboardData,
  getExchangesData,
  addCards,
  getCards,
  updateCard,
  updateUser,
  deleteCard,
  getSingleCard,
  getSingleUser,
  addAffiliatedProduct,
  getAffiliatedProducts,
  getAffiliatedProduct,
  updateAffiliatedProduct,
  deleteAffiliatedProduct,
  createChannel,
  // updateChannelSettings,
} from "../controllers/admin.controllers.js";
import { adminAuth } from "../middlewares/authentication.js";
import { validateBody } from "../middlewares/validateBody.js";
import {
  addCardSchema,
  updateCardSchema,
  addAffiliatedProductSchema,
  updateAffiliatedProductSchema,
  updateUserSchema,
  createChannelSchema,
  // updateChannelSettingsSchema,
} from "../schemaValidation/admin.schema.js";

const router = express.Router();

router.get("/dashboard-data", adminAuth, getDashboardData);
router.get("/user-data", adminAuth, getUsersData);
router.get("/exchanges-data", adminAuth, getExchangesData);
router.get("/blockchains-data", adminAuth, getBlockchainsData);
router.post("/card", adminAuth, validateBody(addCardSchema), addCards);
router.post("/affiliate-product", adminAuth, validateBody(addAffiliatedProductSchema), addAffiliatedProduct);
router.get("/card", adminAuth, getCards);
router.get("/affiliate-product", adminAuth, getAffiliatedProducts);
router.get("/card/:id", adminAuth, getSingleCard);
router.get("/user/:id", adminAuth, getSingleUser);
router.get("/affiliate-product/:id", adminAuth, getAffiliatedProduct);
router.patch(
  "/card/:id",
  adminAuth,
  validateBody(updateCardSchema),
  updateCard
);
router.patch(
  "/user/:id",
  adminAuth,
  validateBody(updateUserSchema),
  updateUser
);
router.patch(
  "/affiliate-product/:id",
  adminAuth,
  validateBody(updateAffiliatedProductSchema),
  updateAffiliatedProduct
);
router.delete("/card/:id", adminAuth, deleteCard);
router.delete("/affiliate-product/:id", adminAuth, deleteAffiliatedProduct);
// Create new channel (admin only with validation)
router.post(
  "/channel",
  adminAuth,
  validateBody(createChannelSchema),
  createChannel
);

// Update channel settings (admin only with validation)
// router.patch(
//   "/channels/:channelId",
//   adminAuth,
//   validateBody(updateChannelSettingsSchema),
//   updateChannelSettings
// );

export default router;
