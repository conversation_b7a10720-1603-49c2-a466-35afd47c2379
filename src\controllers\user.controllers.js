// MongoDb Models
import { User } from "../models/User.js";
import { Earn } from "../models/Earn.js";
import { Card } from "../models/Card.js";
import { Booster } from "../models/Booster.js";
import { AffiliatedProduct } from "../models/AffiliateProducts.js";
import { Chat } from "../models/Chat.js";
import { ethers } from "ethers";

// utils
import { generateController } from "../utils/generateController.js";
import {
  addBonusOnClose,
  addBonusOnOpen,
  bonusPointsCalculator,
} from "../utils/bonusAdder.js";

//
import { ObjectId } from "mongodb";
import moment from "moment";
import { generateUserTokenBackend } from "../utils/helperFunctions.js";

const getGameData = generateController(
  async (request, response, raiseException) => {
    const { userId } = request.user;

    const userData = await User.aggregate([
      {
        $match: { _id: new ObjectId(userId), is_deleted: false },
      },
      {
        $lookup: {
          from: User.collection.name,
          let: { user_tg_id: "$tg_id" },
          pipeline: [
            {
              $match: {
                $expr: { $in: ["$$user_tg_id", "$referrals.tg_id"] },
              },
            },
            {
              $project: {
                tg_id: 1,
              },
            },
          ],
          as: "referred_by",
        },
      },
      {
        $unwind: {
          path: "$referred_by",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $project: {
          tg_id: 1,
          total_points: 1,
          first_name: 1,
          last_name: 1,
          wallet_address: 1,
          in_game_name: 1,
          exchange: 1,
          country: 1,
          referrals: 1,
          social_points: 1,
          jargon_quest: 1,
          claimed_jargon_quest: 1,
          // cards_levels: 1,
          cards_data: 1,
          close_time: 1,
          haptic_feedback: 1,
          availability_status: 1,
          referred_by: "$referred_by.tg_id",
        },
      },
    ]).exec();

    if (!(userData && userData.length > 0)) {
      return raiseException(500, "An error occurred while fetching game data");
    }

    const {
      total_points,
      first_name,
      last_name,
      wallet_address,
      in_game_name,
      exchange,
      country,
      social_points,
      jargon_quest,
      claimed_jargon_quest,
      // cards_levels,
      cards_data,
      haptic_feedback,
    } = userData[0];

    const earnData = await Earn.findOne({ user_id: userData[0]._id });

    if (!earnData) {
      return raiseException(500, "An error occurred while fetching game data");
    }

    const { daily_reward, ...restEarnData } = earnData._doc;

    const lastBuyTime = moment(daily_reward.time);
    const todayTime = moment();

    // Calculate the difference in days
    const differenceInDays = todayTime.diff(lastBuyTime, "days");

    const dailyRewardDays = {
      last_bought: daily_reward.day,
      next_to_buy:
        differenceInDays === 1 && daily_reward.day < 10
          ? daily_reward.day + 1
          : differenceInDays < 1
          ? 0
          : 1,
    };

    const boosterData = await Booster.findOne({ user_id: userData[0]._id });

    if (!boosterData) {
      return raiseException(500, "An error occurred while fetching game data");
    }

    const { energy_level, energy, energy_time, multitap_level } = boosterData;

    const totalEnergy = 1500 + 500 * energy_level;
    let remainingEnergy = energy;

    if (energy < totalEnergy) {
      const parsedTime = moment(energy_time);
      const currentTime = moment();

      // Calculate the difference in seconds
      const differenceInSeconds = currentTime.diff(parsedTime, "seconds");

      const currentEnergy = energy + 3 * differenceInSeconds;
      remainingEnergy =
        currentEnergy >= totalEnergy ? totalEnergy : currentEnergy;

      boosterData.energy = remainingEnergy;
      boosterData.energy_time = currentTime;

      const result = await boosterData.save();

      if (!result) {
        return raiseException(
          500,
          "An error occurred while fetching game data"
        );
      }
    }

    let bonusPoints = 0;

    if (userData[0].cards_data) {
      bonusPoints = await addBonusOnOpen(userData[0]);
      if (bonusPoints === null) {
        return raiseException(500, "Failed to add bonus");
      }
    }

    const AffiliatedProducts = await AffiliatedProduct.aggregate([
      {
        $match: { is_deleted: false },
      },
      {
        $project: {
          _id: 0,
          id: "$_id",
          availability: 1,
          affiliated_link: 1,
          total_clicks: 1,
          image: { $ifNull: ["$image.src", "-"] },
        },
      },
    ]);

    // Get the current ISO date using Moment.js
    const currentDate = moment().toISOString();

    const mineCards = await Card.aggregate([
      {
        $match: {
          is_deleted: false, // Ignore documents where is_deleted is true
        },
      },
      {
        $group: {
          _id: "$deck",
          cards: {
            $push: {
              _id: "$_id",
              deck: "$deck",
              name: "$name",
              description: "$description",
              base_price: "$base_price",
              base_pph: "$base_pph",
              cost_increase_rate: "$cost_increase_rate",
              profit_increase_rate: "$profit_increase_rate",
              image: "$image",
              locked_state: "$locked_state",
              shareable: "$shareable",
              max_level: "$max_level",
              cool_down_period: "$cool_down_period",
              card_availability: "$card_availability",
              affiliated_link: "$affiliated_link",
            },
          },
        },
      },
      {
        $project: {
          // _id: 1,
          // id: "$_id",
          availableCards: {
            $filter: {
              input: "$cards",
              as: "card",
              cond: {
                $and: [
                  { $ifNull: ["$$card.card_availability", false] },
                  { $gte: ["$$card.card_availability", currentDate] },
                ],
              },
            },
          },
          unavailableCards: {
            $filter: {
              input: "$cards",
              as: "card",
              cond: {
                $and: [
                  { $ifNull: ["$$card.card_availability", false] },
                  { $lt: ["$$card.card_availability", currentDate] },
                ],
              },
            },
          },
          otherCards: {
            $filter: {
              input: "$cards",
              as: "card",
              cond: { $not: { $ifNull: ["$$card.card_availability", false] } },
            },
          },
        },
      },
      {
        $project: {
          // _id: 1,
          // id: "$_id",
          cards: {
            $concatArrays: [
              "$availableCards",
              "$otherCards",
              "$unavailableCards",
            ],
          },
        },
      },
      {
        $addFields: {
          formattedDeck: {
            $switch: {
              branches: [
                { case: { $eq: ["$_id", "company"] }, then: "Company" },
                { case: { $eq: ["$_id", "blockchain"] }, then: "Blockchain" },
                { case: { $eq: ["$_id", "psi_book"] }, then: "PSI Book" },
                { case: { $eq: ["$_id", "news"] }, then: "News" },
                { case: { $eq: ["$_id", "special"] }, then: "Special" },
              ],
              default: "$_id",
            },
          },
        },
      },
      {
        $group: {
          _id: null,
          decks: {
            $push: {
              k: "$formattedDeck",
              v: {
                // id: "$id",
                cards: "$cards",
              },
            },
          },
        },
      },
      {
        $project: {
          _id: 0,
          decks: {
            $arrayToObject: "$decks",
          },
        },
      },
      {
        $set: {
          "decks.Company": {
            $ifNull: ["$decks.Company", { id: "company", cards: [] }],
          },
          "decks.Blockchain": {
            $ifNull: ["$decks.Blockchain", { id: "blockchain", cards: [] }],
          },
          "decks.PSI Book": {
            $ifNull: ["$decks.PSI Book", { id: "psi_book", cards: [] }],
          },
          "decks.News": { $ifNull: ["$decks.News", { id: "news", cards: [] }] },
          "decks.Special": {
            $ifNull: ["$decks.Special", { id: "special", cards: [] }],
          },
        },
      },
    ]);

    if (!(mineCards && mineCards.length > 0)) {
      return raiseException(500, "An error occurred while fetching game data");
    }

    const inGameName =
      in_game_name || `${first_name} ${last_name || ""}`.trim();

    const tgId = userData[0].tg_id;

    const chats = await Chat.find({
      room_id: { $regex: new RegExp(`^${tgId}_|_${tgId}$`) },
    }).exec();

    // Convert array to object format
    const chatObject = chats.reduce((acc, chat) => {
      acc[chat.room_id] = chat.messages;
      return acc;
    }, {});

    return {
      message: "Game data fetched successfully",
      payload: {
        total_points,
        exchange_points: bonusPoints,
        wallet_address,
        in_game_name: inGameName,
        exchange,
        country,
        // cards_levels,
        claimed_jargon_quest,
        cards_data,
        social_points,
        haptic_feedback,
        jargon_quest,
        energy_level,
        total_energy: totalEnergy,
        remaining_energy: remainingEnergy,
        multitap_level,
        earn: {
          ...restEarnData,
          daily_reward_days: dailyRewardDays,
        },
        mine_cards: mineCards[0].decks,
        affiliated_products: AffiliatedProducts,
        chats: chatObject,
      },
    };
  }
);

const getWebData = generateController(
  async (request, response, raiseException) => {
    const { tg_id } = request.query;

    const userData = await User.findOne(
      { tg_id, is_deleted: false },
      {
        in_game_name: 1,
        first_name: 1,
        last_name: 1,
        social_points: 1,
      }
    );

    if (!userData) {
      return raiseException(500, "An error occurred while Fetching User Data");
    }

    const { in_game_name, first_name, last_name, social_points } = userData;

    const inGameName =
      in_game_name || `${first_name} ${last_name || ""}`.trim();

    return {
      message: "User data fetched successfully",
      payload: {
        in_game_name: inGameName,
        social_points,
      },
    };
  }
);

const affiliateProductInteraction = generateController(
  async (request, response, raiseException) => {
    const { product_id } = request.body;
    const { userId } = request.user;
    // console.log(product_id)

    const userData = await User.findOne(
      { _id: userId, is_deleted: false },
      {
        affiliate_product_interactions: 1,
        social_points: 1,
      }
    );

    if (!userData) {
      return raiseException(500, "An error occurred while updating points");
    }

    const affiliateProduct = await AffiliatedProduct.findById(
      product_id
    ).exec();
    // console.log(affiliateProduct)

    if (!affiliateProduct) {
      return raiseException(
        500,
        "An error occurred while updating social points"
      );
    }

    if (!affiliateProduct.total_clicks) {
      affiliateProduct.total_clicks = 1;
    } else {
      affiliateProduct.total_clicks += 1;
    }
    affiliateProduct.markModified("total_clicks");

    if (!userData.affiliate_product_interactions) {
      userData.affiliate_product_interactions = {};
    }
    if (
      userData?.affiliate_product_interactions[product_id] &&
      moment().isBefore(
        moment(userData?.affiliate_product_interactions[product_id]).add(
          24,
          "hours"
        )
      )
    ) {
      await affiliateProduct.save();
      return raiseException(500, "Social point already claimed");
    }

    userData.affiliate_product_interactions[product_id] =
      moment().toISOString(); // record current time of interation
    if (!userData.social_points) {
      userData.social_points = 1;
    } else {
      userData.social_points += 1;
    }

    // Mark the field as modified
    userData.markModified("affiliate_product_interactions");
    userData.markModified("social_points");

    await affiliateProduct.save();
    const result = await userData.save();
    if (!result) {
      return raiseException(
        500,
        "An error occurred while updating social points"
      );
    }

    const { social_points, affiliate_product_interactions } = result;

    return {
      message: "Social points added successfully",
      payload: { social_points, affiliate_product_interactions },
    };
  }
);

const socialCardInteraction = generateController(
  async (request, response, raiseException) => {
    const { card_id: cardId } = request.body;
    const { userId } = request.user;
    // console.log(product_id)

    const userData = await User.findOne(
      { _id: userId, is_deleted: false },
      {
        social_card_interactions: 1,
        social_points: 1,
      }
    );

    if (!userData) {
      return raiseException(500, "An error occurred while updating points");
    }

    const card = await Card.findOne(
      { _id: cardId, is_deleted: false },
      {
        shareable: 1,
      }
    );

    // Check if card exists and is shareable
    if (!card) {
      return raiseException(404, "Card not found");
    }

    if (!card.shareable) {
      return raiseException(403, "This card is not shareable");
    }

    if (!userData.social_card_interactions) {
      userData.social_card_interactions = {};
    }
    if (
      userData?.social_card_interactions[cardId] &&
      moment().isBefore(
        moment(userData?.social_card_interactions[cardId]).add(24, "hours")
      )
    ) {
      return raiseException(500, "Social point already claimed");
    }

    userData.social_card_interactions[cardId] = moment().toISOString(); // record current time of interation
    if (!userData.social_points) {
      userData.social_points = 1;
    } else {
      userData.social_points += 1;
    }

    // Mark the field as modified
    userData.markModified("social_card_interactions");
    userData.markModified("social_points");

    const result = await userData.save();
    if (!result) {
      return raiseException(
        500,
        "An error occurred while updating social points"
      );
    }

    const { social_points, social_card_interactions } = result;

    return {
      message: "Social points added successfully",
      payload: { social_points, social_card_interactions },
    };
  }
);

const SocialPointsPurchase = generateController(
  async (request, response, raiseException) => {
    const { social_points_bought, transactionHash, userReferee } = request.body;
    const { userId } = request.user;

    const userData = await User.findOne(
      { _id: userId, is_deleted: false },
      {
        social_points: 1,
      }
    );

    if (!userData) {
      return raiseException(500, "An error occurred while updating points");
    }

    const provider = new ethers.JsonRpcProvider(process.env.RPC_URL);

    const txReceipt = await provider.waitForTransaction(transactionHash, 1); // Wait for 1 confirmation

    // If transaction failed or was reverted
    if (!txReceipt.status) {
      return raiseException(400, "Transaction failed on the blockchain");
    }

    // Verify the transaction is from the user's wallet and our wallet
    if (
      txReceipt.to.toLowerCase() !==
      process.env.INGAME_PURCHASING_FUNDS_ADDRESS.toLowerCase()
    ) {
      return raiseException(400, "Transaction is not from the user's wallet");
    }
    if (!userData.social_points) {
      userData.social_points = social_points_bought;
    } else {
      userData.social_points += social_points_bought;
    }

    if (userReferee != 0) {
      const refereeData = await User.findOne(
        { tg_id: userReferee, is_deleted: false },
        {
          social_points: 1,
        }
      );

      if (refereeData) {
        if (!refereeData.social_points) {
          refereeData.social_points = social_points_bought * 0.1;
        } else {
          refereeData.social_points += social_points_bought * 0.1;
        }
        refereeData.markModified("social_points");
        await refereeData.save();
      }
    }

    // Mark the field as modified
    userData.markModified("social_points");

    const result = await userData.save();
    if (!result) {
      return raiseException(
        500,
        "An error occurred while updating social points"
      );
    }

    const { social_points } = result;

    return {
      message: "Social points bought successfully",
      payload: { social_points },
    };
  }
);

const SocialPointsScratcher = generateController(
  async (request, response, raiseException) => {
    const { tg_id } = request.body;

    const userData = await User.findOne(
      { tg_id, is_deleted: false },
      {
        social_points: 1,
      }
    );

    if (!userData) {
      return raiseException(500, "An error occurred while updating points");
    }

    if (!userData.social_points) {
      userData.social_points = 1;
    } else {
      userData.social_points += 1;
    }

    // Mark the field as modified
    userData.markModified("social_points");

    const result = await userData.save();
    if (!result) {
      return raiseException(
        500,
        "An error occurred while updating social points"
      );
    }

    const { social_points } = result;

    return {
      message: "Social points Rewarded successfully",
      payload: { social_points },
    };
  }
);

const FreezeSocialPoints = generateController(
  async (request, response, raiseException) => {
    const { token, tg_id, spToFreeze } = request.body;

    if (!token || token.length !== 12) {
      return raiseException(400, "Invalid token format");
    }

    const userData = await User.findOne(
      { tg_id, is_deleted: false },
      {
        _id: 1,
        social_points: 1,
        frozen_social_points: 1,
      }
    );

    if (!userData) {
      return raiseException(500, "An error occurred while updating points");
    }

    const generatedHash = generateUserTokenBackend(
      userData._id.toString(),
      tg_id
    );

    if (generatedHash !== token) {
      return raiseException(400, "Invalid token");
    }
    if (!userData.social_points || !(userData.social_points >= spToFreeze))
      return raiseException(400, "Insufficient social points to freeze");

    if (!userData.frozen_social_points) {
      userData.frozen_social_points = spToFreeze;
      userData.social_points -= spToFreeze;
    } else {
      userData.social_points -= spToFreeze;
      userData.frozen_social_points += spToFreeze;
    }

    // Mark the field as modified
    userData.markModified("social_points");
    userData.markModified("frozen_social_points");

    const result = await userData.save();
    if (!result) {
      return raiseException(
        500,
        "An error occurred while updating social points"
      );
    }

    const { social_points, frozen_social_points } = result;

    return {
      message: "Social points Frozen successfully",
      payload: { social_points, frozen_social_points },
    };
  }
);

const UnfreezeSocialPoints = generateController(
  async (request, response, raiseException) => {
    const { token, tg_id, spToUnfreeze } = request.body;

    if (!token || token.length !== 12) {
      return raiseException(400, "Invalid token format");
    }

    const userData = await User.findOne(
      { tg_id, is_deleted: false },
      {
        _id: 1,
        social_points: 1,
        frozen_social_points: 1,
      }
    );

    if (!userData) {
      return raiseException(500, "An error occurred while updating points");
    }

    const generatedHash = generateUserTokenBackend(
      userData._id.toString(),
      tg_id
    );

    if (generatedHash !== token) {
      return raiseException(400, "Invalid token");
    }
    if (
      !userData.frozen_social_points ||
      !(userData.frozen_social_points >= spToUnfreeze)
    )
      return raiseException(
        400,
        "Insufficient Frozen social points to Unfreeze"
      );

    if (!userData.social_points) {
      userData.social_points = spToUnfreeze;
      userData.frozen_social_points -= spToUnfreeze;
    } else {
      userData.social_points += spToUnfreeze;
      userData.frozen_social_points -= spToUnfreeze;
    }

    // Mark the field as modified
    userData.markModified("social_points");
    userData.markModified("frozen_social_points");

    const result = await userData.save();
    if (!result) {
      return raiseException(
        500,
        "An error occurred while updating social points"
      );
    }

    const { social_points, frozen_social_points } = result;

    return {
      message: "Social points Unfrozen successfully",
      payload: { social_points, frozen_social_points },
    };
  }
);

const updatePoints = generateController(
  async (request, response, raiseException) => {
    const { taps, energy } = request.body;
    const { userId } = request.user;

    const userData = await User.findOneAndUpdate(
      { _id: userId, is_deleted: false },
      {
        $inc: { total_points: taps },
      }
    );

    if (!userData) {
      return raiseException(500, "An error occurred while updating points");
    }

    const boosterData = await Booster.findOneAndUpdate(
      { user_id: userId },
      {
        $set: { energy, energy_time: moment().toISOString() },
      }
    );

    if (!boosterData) {
      return raiseException(500, "An error occurred while updating points");
    }

    return {
      message: "Points updated successfully",
    };
  }
);

const updateWalletAddress = generateController(
  async (request, response, raiseException) => {
    const { wallet_address } = request.body;
    const { userId } = request.user;

    const userData = await User.findOneAndUpdate(
      { _id: userId, is_deleted: false },
      {
        $set: { wallet_address },
      },
      { new: true }
    );

    if (!userData) {
      return raiseException(
        500,
        "An error occurred while updating wallet address"
      );
    }

    return {
      message: "Wallet address updated successfully",
      payload: { wallet_address: userData.wallet_address },
    };
  }
);

const updateInGameName = generateController(
  async (request, response, raiseException) => {
    const { in_game_name } = request.body;
    const { userId } = request.user;

    const userData = await User.findOneAndUpdate(
      { _id: userId, is_deleted: false },
      {
        $set: { in_game_name },
      },
      { new: true }
    );

    if (!userData) {
      return raiseException(
        500,
        "An error occurred while updating in game name"
      );
    }

    return {
      message: "In game name updated successfully",
      payload: { in_game_name: userData.in_game_name },
    };
  }
);

const updateExchange = generateController(
  async (request, response, raiseException) => {
    const { exchange } = request.body;
    const { userId } = request.user;

    const userData = await User.findOneAndUpdate(
      { _id: userId, is_deleted: false },
      {
        $set: { exchange },
      }
    );

    if (!userData) {
      return raiseException(500, "An error occurred while updating exchange");
    }

    return {
      message: "Exchange updated successfully",
    };
  }
);

const updateCountry = generateController(
  async (request, response, raiseException) => {
    const { country } = request.body;
    const { userId } = request.user;

    const userData = await User.findOneAndUpdate(
      { _id: userId, is_deleted: false },
      {
        $set: { country },
      }
    );

    if (!userData) {
      return raiseException(500, "An error occurred while updating country");
    }

    return {
      message: "Country updated successfully",
    };
  }
);

const cardPurchase = generateController(
  async (request, response, raiseException) => {
    const { card_id, buy_time } = request.body;
    const { userId } = request.user;

    const userData = await User.findOne(
      { _id: userId, is_deleted: false },
      {
        total_points: 1,
        // cards_levels: 1,
        cards_data: 1,
        jargon_quest: 1,
        start_time: 1,
      }
    );

    if (!userData) {
      return raiseException(500, "An error occurred while updating card level");
    }

    const bonusPoints = await bonusPointsCalculator(userData, buy_time);
    if (bonusPoints === null) {
      return raiseException(500, "An error occurred while updating card level");
    }

    const card = await Card.findById(card_id).exec();

    if (!card) {
      return raiseException(500, "An error occurred while updating card level");
    }
    if (
      card.card_availability &&
      moment().isAfter(moment(card.card_availability))
    ) {
      return raiseException(500, "Card Upgrade time ended.");
    }
    // REVIEW
    // if (!card.max_level || card.max_level > (userData.cards_levels?.[card_id] || 0)) {
    if (
      !card.max_level ||
      card.max_level > (userData.cards_data?.[card_id]?.level || 0)
    ) {
      const cardPrice =
        card.base_price *
        // REVIEW
        // card.cost_increase_rate ** (userData.cards_levels?.[card_id] || 0);
        card.cost_increase_rate ** (userData.cards_data?.[card_id]?.level || 0);

      const updatedPoints =
        userData.total_points + bonusPoints.points - cardPrice;

      if (updatedPoints < 0) {
        return raiseException(406, "Insufficient funds");
      }

      userData.total_points = updatedPoints;
      userData.start_time = bonusPoints.buyTime.toISOString();

      // REVIEW
      // // If cards_levels is undefined, initialize it
      // if (!userData.cards_levels) {
      //   userData.cards_levels = {};
      // }
      if (!userData.cards_data) {
        userData.cards_data = {};
      }
      // REVIEW
      const current_upgrade_time = moment().toISOString();
      const cardData = userData.cards_data;

      if (cardData[card_id] && card.cool_down_period) {
        const coolDownEndTime = moment(cardData[card_id].card_upgrade_time).add(
          card.cool_down_period,
          "hours"
        );
        if (moment().isAfter(coolDownEndTime)) {
          cardData[card_id].level = (cardData[card_id].level || 0) + 1;
          cardData[card_id].card_upgrade_time = current_upgrade_time;
        } else {
          return raiseException(500, "Cooldown is Active.");
        }
      } else {
        cardData[card_id] = {
          level: (cardData[card_id]?.level || 0) + 1,
          card_upgrade_time: current_upgrade_time,
        };
      }
      // P3 SEC1
      if (card.id == "BSC" && cardData[card_id].level == 20) {
        userData.jargon_quest += 500;
      }

      // Mark the field as modified
      userData.markModified("cards_data");

      const result = await userData.save();

      if (!result) {
        return raiseException(
          500,
          "An error occurred while updating card level"
        );
      }

      const { total_points, cards_data, jargon_quest } = result;

      return {
        message: "Card purchased successfully",
        payload: { total_points, cards_data, jargon_quest },
      };
    } else {
      return raiseException(500, "Max Level Reached.");
    }
  }
);

const boosterPurchase = generateController(
  async (request, response, raiseException) => {
    const { type, buy_time } = request.body;
    const { userId } = request.user;

    const userData = await User.findOne(
      { _id: userId, is_deleted: false },
      {
        total_points: 1,
        // cards_levels: 1,
        cards_data: 1,
        start_time: 1,
      }
    );

    if (!userData) {
      return raiseException(500, "An error occurred while updating booster");
    }

    const bonusPoints = await bonusPointsCalculator(userData, buy_time);
    if (bonusPoints === null) {
      return raiseException(500, "An error occurred while updating booster");
    }

    const boosterData = await Booster.findOne(
      { user_id: userId },
      { [`${type}_level`]: 1 }
    );

    if (!boosterData) {
      return raiseException(500, "An error occurred while updating booster");
    }

    const updatedPoints =
      userData.total_points +
      bonusPoints.points -
      1000 * 2 ** boosterData[`${type}_level`];

    if (updatedPoints < 0) {
      return raiseException(406, "Insufficient funds");
    }

    userData.total_points = updatedPoints;
    if (bonusPoints.points)
      userData.start_time = bonusPoints.buyTime.toISOString();

    const result = await userData.save();

    if (!result) {
      return raiseException(500, "An error occurred while updating booster");
    }

    boosterData[`${type}_level`] += 1;

    const boosterResult = await boosterData.save();

    if (!boosterResult) {
      return raiseException(500, "An error occurred while updating booster");
    }

    const { total_points } = result;

    const totalEnergy =
      type === "energy"
        ? { total_energy: 1500 + 500 * boosterData[`${type}_level`] }
        : {};

    return {
      message: "Booster purchased successfully",
      payload: {
        total_points,
        [`${type}_level`]: boosterData[`${type}_level`],
        ...totalEnergy,
      },
    };
  }
);

const convertSocialPoints = generateController(
  async (request, response, raiseException) => {
    // const { type, buy_time } = request.body;
    const { userId } = request.user;

    const userData = await User.findOne(
      { _id: userId, is_deleted: false },
      {
        social_points: 1,
        jargon_quest: 1,
      }
    );

    if (!userData) {
      return raiseException(500, "An error occurred while updating Jq Points");
    }

    if (!userData.social_points) {
      return raiseException(406, "Insufficient Social Points");
    }

    // if (userData.social_points < 0) {
    //   return raiseException(406, "Insufficient Social Points");
    // }

    userData.jargon_quest += userData.social_points;

    // update Social Points
    userData.social_points = 0;

    const result = await userData.save();

    if (!result) {
      return raiseException(500, "An error occurred while updating Jq Points");
    }

    const { social_points, jargon_quest } = result;

    return {
      message: "Social Points Converted successfully",
      payload: {
        social_points,
        jargon_quest,
      },
    };
  }
);

const claimJqPoints = generateController(
  async (request, response, raiseException) => {
    const { userId } = request.user;

    const userData = await User.findOne(
      { _id: userId, is_deleted: false },
      {
        wallet_address: 1,
        jargon_quest: 1,
      }
    );

    if (!userData) {
      return raiseException(500, "An error occurred while updating Jq Points");
    }

    if (!userData.jargon_quest) {
      return raiseException(406, "Insufficient Jq Points");
    }

    if (!userData.wallet_address) {
      return raiseException(400, "Wallet address not set");
    }

    // Check if the address is a valid Ethereum address
    if (!ethers.isAddress(userData.wallet_address)) {
      return raiseException(400, "Invalid Ethereum address");
    }
    const privateKey = process.env.WALLET_PRIVATE_KEY;
    const wallet = new ethers.Wallet(privateKey);

    // Set deadline 1 hour in the future
    const deadline = Math.floor(Date.now() / 1000) + 3600;
    console.log(userData);

    const amount = ethers.parseEther(
      Math.min(userData.jargon_quest, 250).toString()
    );
    // Create message hash
    const messageHash = ethers.solidityPackedKeccak256(
      ["address", "uint256", "uint256", "address"],
      [
        userData.wallet_address,
        amount,
        deadline,
        process.env.CLAIM_CONTRACT_ADDRESS,
      ]
    );

    // Sign the hash
    const signature = await wallet.signMessage(ethers.getBytes(messageHash));
    console.log(amount.toString(), deadline, signature);

    return {
      message: "Signed transaction generated successfully",
      payload: {
        amount: amount.toString(),
        deadline,
        signature,
      },
    };
  }
);

const jqPointsClaimed = generateController(
  async (request, response, raiseException) => {
    const { userId } = request.user;
    const { transactionHash } = request.body;

    // Validate transaction hash is provided
    if (!transactionHash) {
      return raiseException(400, "Transaction hash is required");
    }

    // Find the user
    const userData = await User.findOne(
      { _id: userId, is_deleted: false },
      {
        social_points: 1,
        wallet_address: 1,
        jargon_quest: 1,
        claimed_jargon_quest: 1,
      }
    );

    if (!userData) {
      return raiseException(500, "An error occurred while finding user data");
    }

    if (!userData.jargon_quest) {
      return raiseException(406, "Insufficient Jq Points");
    }

    // Initialize provider (BSC in this case)
    const provider = new ethers.JsonRpcProvider(process.env.RPC_URL);

    // Wait for the transaction to be mined and confirmed
    const txReceipt = await provider.waitForTransaction(transactionHash, 1); // Wait for 1 confirmation

    // If transaction failed or was reverted
    if (!txReceipt.status) {
      return raiseException(400, "Transaction failed on the blockchain");
    }

    // Verify the transaction is for our contract
    const contractAddress = process.env.CLAIM_CONTRACT_ADDRESS.toLowerCase();
    if (txReceipt.to.toLowerCase() !== contractAddress) {
      return raiseException(400, "Transaction is not for the correct contract");
    }

    // Verify the transaction is from the user's wallet
    if (
      txReceipt.from.toLowerCase() !== userData.wallet_address.toLowerCase()
    ) {
      return raiseException(400, "Transaction is not from the user's wallet");
    }

    // At this point, we've verified the transaction is successful and legitimate
    // Update the user's points
    userData.claimed_jargon_quest += Math.min(userData.jargon_quest, 250);
    userData.jargon_quest -= Math.min(userData.jargon_quest, 250);

    const result = await userData.save();

    if (!result) {
      return raiseException(500, "An error occurred while updating Jq Points");
    }

    const { claimed_jargon_quest, jargon_quest } = result;

    return {
      message: "Jq Points Claimed successfully",
      payload: {
        claimed_jargon_quest,
        jargon_quest,
        transaction: transactionHash,
      },
    };
  }
);

const getTopRankedUsers = generateController(
  async (request, response, raiseException) => {
    const { from, to } = request.query;
    const { userId } = request.user;

    const mineCards = await Card.find().exec();

    if (!mineCards) {
      return raiseException(500, "An error occurred fetching top ranked users");
    }

    // const users = await User.aggregate([
    //   // Filter users with total_points between 10 and 10,000
    //   {
    //     $match: {
    //       total_points: { $gte: parseFloat(from), $lte: parseFloat(to) },
    //       is_deleted: false,
    //     },
    //   },

    //   // Add the `pph` key based on mineCards data and calculate pph
    //   {
    //     $addFields: {
    //       cards_levels: {
    //         $cond: {
    //           if: { $eq: [{ $type: "$cards_levels" }, "missing"] }, // Check if cards_levels is missing
    //           then: [], // If missing, use an empty array
    //           else: {
    //             $map: {
    //               input: { $objectToArray: "$cards_levels" }, // Convert cards_levels to an array of key-value pairs
    //               as: "role",
    //               in: {
    //                 k: "$$role.k", // Keep the original key
    //                 v: {
    //                   $mergeObjects: [
    //                     {
    //                       pph: {
    //                         $let: {
    //                           vars: {
    //                             // Find the corresponding card from mineCards by matching the id
    //                             card: {
    //                               $first: {
    //                                 $filter: {
    //                                   input: mineCards,
    //                                   as: "card",
    //                                   cond: { $eq: ["$$card._id", "$$role.k"] },
    //                                 },
    //                               },
    //                             },
    //                           },
    //                           in: {
    //                             $multiply: [
    //                               "$$card.base_pph",
    //                               {
    //                                 $divide: [
    //                                   {
    //                                     $subtract: [
    //                                       {
    //                                         $pow: [
    //                                           "$$card.profit_increase_rate",
    //                                           "$$role.v",
    //                                         ],
    //                                       },
    //                                       1,
    //                                     ],
    //                                   },
    //                                   {
    //                                     $subtract: [
    //                                       "$$card.profit_increase_rate",
    //                                       1,
    //                                     ],
    //                                   },
    //                                 ],
    //                               },
    //                             ],
    //                           },
    //                         },
    //                       },
    //                     },
    //                   ],
    //                 },
    //               },
    //             },
    //           },
    //         },
    //       },
    //     },
    //   },
    //   {
    //     $addFields: {
    //       cards_levels: { $arrayToObject: "$cards_levels" }, // Convert back to object
    //     },
    //   },

    //   // Sum the `pph` values or set total_pph to 0 if there are no cards_levels
    //   {
    //     $addFields: {
    //       total_pph: {
    //         $cond: {
    //           if: { $gt: [{ $size: { $objectToArray: "$cards_levels" } }, 0] },
    //           then: {
    //             $reduce: {
    //               input: { $objectToArray: "$cards_levels" },
    //               initialValue: 0,
    //               in: { $add: ["$$value", "$$this.v.pph"] },
    //             },
    //           },
    //           else: 0,
    //         },
    //       },
    //     },
    //   },

    //   // Sort by total_pph in descending order
    //   {
    //     $sort: { total_pph: -1 },
    //   },

    //   // Add a rank field based on the position in the array
    //   {
    //     $group: {
    //       _id: null,
    //       users: { $push: "$$ROOT" },
    //     },
    //   },
    //   {
    //     $unwind: {
    //       path: "$users",
    //       includeArrayIndex: "rank", // Add the index of the document in the array
    //     },
    //   },
    //   {
    //     $addFields: {
    //       "users.rank": { $add: [1, "$rank"] }, // Rank starts from 1
    //     },
    //   },
    //   {
    //     $replaceRoot: { newRoot: "$users" }, // Flatten the document structure
    //   },

    //   // Separating top 100 and Specific User
    //   {
    //     $facet: {
    //       topUsers: [
    //         // Select the top users
    //         { $limit: 100 },
    //       ],
    //       specificUser: [
    //         // Find and include the specific user
    //         { $match: { _id: new ObjectId(userId) } },
    //       ],
    //     },
    //   },

    //   // Check if the specific user is already in the top users
    //   {
    //     $addFields: {
    //       combined: {
    //         $let: {
    //           vars: {
    //             isSpecificUserInTop: {
    //               $in: [
    //                 new ObjectId(userId),
    //                 {
    //                   $map: {
    //                     input: "$topUsers",
    //                     as: "user",
    //                     in: "$$user._id",
    //                   },
    //                 },
    //               ],
    //             },
    //             specificUserExists: {
    //               $gt: [{ $size: "$specificUser" }, 0], // Check if specificUser exists
    //             },
    //           },
    //           in: {
    //             $cond: {
    //               if: {
    //                 $and: [
    //                   { $not: "$$isSpecificUserInTop" }, // Ensure user is not in topUsers
    //                   "$$specificUserExists", // Ensure user exists
    //                 ],
    //               },
    //               then: { $concatArrays: ["$topUsers", "$specificUser"] }, // Add user if not present
    //               else: "$topUsers", // Otherwise, return topUsers unchanged
    //             },
    //           },
    //         },
    //       },
    //     },
    //   },

    //   // Replace the root with the combined array
    //   {
    //     $project: {
    //       combined: 1,
    //     },
    //   },
    //   {
    //     $unwind: "$combined",
    //   },
    //   {
    //     $replaceRoot: { newRoot: "$combined" },
    //   },

    //   // Project only the specified fields
    //   {
    //     $project: {
    //       in_game_name: {
    //         $ifNull: [
    //           "$in_game_name",
    //           { $concat: ["$first_name", " ", "$last_name"] },
    //         ],
    //       },
    //       exchange: 1,
    //       total_pph: 1,
    //       rank: 1,
    //     },
    //   },
    // ]).exec();

    const users = await User.aggregate([
      // Step 1: Filter users by total_points and is_deleted flag
      {
        $match: {
          total_points: { $gte: parseFloat(from), $lte: parseFloat(to) },
          is_deleted: false,
        },
      },

      // Step 2: Add pph key to cards_data using mineCards
      {
        $addFields: {
          cards_data: {
            $cond: {
              if: { $eq: [{ $type: "$cards_data" }, "missing"] }, // Check if cards_data is missing
              then: [],
              else: {
                $map: {
                  input: { $objectToArray: "$cards_data" }, // Convert cards_data to key-value array
                  as: "role",
                  in: {
                    k: "$$role.k", // Key remains the same
                    v: {
                      $mergeObjects: [
                        {
                          pph: {
                            $let: {
                              vars: {
                                card: {
                                  $first: {
                                    $filter: {
                                      input: mineCards,
                                      as: "card",
                                      cond: { $eq: ["$$card._id", "$$role.k"] },
                                    },
                                  },
                                },
                              },
                              in: {
                                $multiply: [
                                  "$$card.base_pph",
                                  {
                                    $divide: [
                                      {
                                        $subtract: [
                                          {
                                            $pow: [
                                              "$$card.profit_increase_rate",
                                              "$$role.v.level",
                                            ],
                                          },
                                          1,
                                        ],
                                      },
                                      {
                                        $subtract: [
                                          "$$card.profit_increase_rate",
                                          1,
                                        ],
                                      },
                                    ],
                                  },
                                ],
                              },
                            },
                          },
                        },
                      ],
                    },
                  },
                },
              },
            },
          },
        },
      },
      { $addFields: { cards_data: { $arrayToObject: "$cards_data" } } }, // Convert cards_data back to an object

      // Step 3: Calculate total_pph
      {
        $addFields: {
          total_pph: {
            $cond: {
              if: { $gt: [{ $size: { $objectToArray: "$cards_data" } }, 0] },
              then: {
                $reduce: {
                  input: { $objectToArray: "$cards_data" },
                  initialValue: 0,
                  in: { $add: ["$$value", "$$this.v.pph"] },
                },
              },
              else: 0,
            },
          },
        },
      },

      // Step 4: Sort users by total_pph descending
      { $sort: { total_pph: -1 } },

      // Step 5: Add ranks to users
      {
        $group: {
          _id: null,
          users: { $push: "$$ROOT" },
        },
      },
      {
        $unwind: {
          path: "$users",
          includeArrayIndex: "rank",
        },
      },
      {
        $addFields: {
          "users.rank": { $add: ["$rank", 1] }, // Rank starts at 1
        },
      },
      { $replaceRoot: { newRoot: "$users" } },

      // Step 6: Separate top 100 users and specific user
      {
        $facet: {
          topUsers: [{ $limit: 100 }], // Get top 100 users
          specificUser: [{ $match: { _id: new ObjectId(userId) } }], // Find specific user by ID
        },
      },

      // Step 7: Add specific user to topUsers if not already present
      {
        $addFields: {
          combined: {
            $let: {
              vars: {
                isSpecificUserInTop: {
                  $in: [
                    new ObjectId(userId),
                    {
                      $map: {
                        input: "$topUsers",
                        as: "user",
                        in: "$$user._id",
                      },
                    },
                  ],
                },
                specificUserExists: { $gt: [{ $size: "$specificUser" }, 0] },
              },
              in: {
                $cond: {
                  if: {
                    $and: [
                      { $not: "$$isSpecificUserInTop" }, // Ensure user not in topUsers
                      "$$specificUserExists", // User exists
                    ],
                  },
                  then: { $concatArrays: ["$topUsers", "$specificUser"] }, // Add user
                  else: "$topUsers", // Keep topUsers unchanged
                },
              },
            },
          },
        },
      },

      // Step 8: Unwind combined array to flatten the structure
      { $project: { combined: 1 } },
      { $unwind: "$combined" },
      { $replaceRoot: { newRoot: "$combined" } },

      // Step 9: Project required fields
      {
        $project: {
          in_game_name: {
            $ifNull: [
              "$in_game_name",
              {
                $trim: {
                  input: {
                    $concat: [
                      "$first_name",
                      { $cond: [{ $ifNull: ["$last_name", false] }, " ", ""] },
                      { $ifNull: ["$last_name", ""] },
                    ],
                  },
                },
              },
            ],
          },
          exchange: 1,
          total_pph: 1,
          rank: 1,
        },
      },
    ]).exec();

    if (!users) {
      return raiseException(500, "An error occurred fetching top ranked users");
    }

    return {
      message: "Top ranked users fetched successfully",
      payload: { users },
    };
  }
);

const getInvitedFriends = generateController(
  async (request, response, raiseException) => {
    const { userId } = request.user;

    const mineCards = await Card.find().exec();

    if (!mineCards) {
      return raiseException(
        500,
        "An error occurred while fetching invited friends"
      );
    }

    // const invitedFriends = await User.aggregate([
    //   {
    //     $match: { _id: new ObjectId(userId), is_deleted: false },
    //   },
    //   {
    //     $lookup: {
    //       from: User.collection.name,
    //       localField: "referrals",
    //       foreignField: "tg_id",
    //       as: "invited_friends",
    //       pipeline: [
    //         {
    //           $addFields: {
    //             cards_levels: {
    //               $cond: {
    //                 if: { $eq: [{ $type: "$cards_levels" }, "missing"] }, // Check if cards_levels is missing
    //                 then: [], // If missing, use an empty array
    //                 else: {
    //                   $map: {
    //                     input: { $objectToArray: "$cards_levels" }, // Convert cards_levels to an array of key-value pairs
    //                     as: "role",
    //                     in: {
    //                       k: "$$role.k", // Keep the original key
    //                       v: {
    //                         $mergeObjects: [
    //                           {
    //                             pph: {
    //                               $let: {
    //                                 vars: {
    //                                   // Find the corresponding card from mineCards by matching the id
    //                                   card: {
    //                                     $first: {
    //                                       $filter: {
    //                                         input: mineCards,
    //                                         as: "card",
    //                                         cond: {
    //                                           $eq: ["$$card._id", "$$role.k"],
    //                                         },
    //                                       },
    //                                     },
    //                                   },
    //                                 },
    //                                 in: {
    //                                   $multiply: [
    //                                     "$$card.base_pph",
    //                                     {
    //                                       $divide: [
    //                                         {
    //                                           $subtract: [
    //                                             {
    //                                               $pow: [
    //                                                 "$$card.profit_increase_rate",
    //                                                 "$$role.v",
    //                                               ],
    //                                             },
    //                                             1,
    //                                           ],
    //                                         },
    //                                         {
    //                                           $subtract: [
    //                                             "$$card.profit_increase_rate",
    //                                             1,
    //                                           ],
    //                                         },
    //                                       ],
    //                                     },
    //                                   ],
    //                                 },
    //                               },
    //                             },
    //                           },
    //                         ],
    //                       },
    //                     },
    //                   },
    //                 },
    //               },
    //             },
    //           },
    //         },
    //         {
    //           $addFields: {
    //             cards_levels: { $arrayToObject: "$cards_levels" }, // Convert back to object
    //           },
    //         },
    //         // Sum the `pph` values or set total_pph to 0 if there are no cards_levels
    //         {
    //           $addFields: {
    //             total_pph: {
    //               $cond: {
    //                 if: {
    //                   $gt: [{ $size: { $objectToArray: "$cards_levels" } }, 0],
    //                 },
    //                 then: {
    //                   $reduce: {
    //                     input: { $objectToArray: "$cards_levels" },
    //                     initialValue: 0,
    //                     in: { $add: ["$$value", "$$this.v.pph"] },
    //                   },
    //                 },
    //                 else: 0,
    //               },
    //             },
    //           },
    //         },
    //         {
    //           $project: {
    //             is_premium: 1,
    //             in_game_name: {
    //               $ifNull: [
    //                 "$in_game_name",
    //                 { $concat: ["$first_name", " ", "$last_name"] },
    //               ],
    //             },
    //             total_points: 1,
    //             total_pph: 1,
    //           },
    //         },
    //       ],
    //     },
    //   },
    //   {
    //     $project: {
    //       _id: 0,
    //       invited_friends: 1,
    //       total_points: 1,
    //     },
    //   },
    // ]).exec();

    const invitedFriends = await User.aggregate([
      {
        $match: { _id: new ObjectId(userId), is_deleted: false },
      },
      {
        $lookup: {
          from: User.collection.name,
          localField: "referrals.tg_id",
          foreignField: "tg_id",
          as: "invited_friends",
          pipeline: [
            {
              $addFields: {
                cards_data: {
                  $cond: {
                    if: { $eq: [{ $type: "$cards_data" }, "missing"] },
                    then: [],
                    else: {
                      $map: {
                        input: { $objectToArray: "$cards_data" },
                        as: "role",
                        in: {
                          k: "$$role.k",
                          v: {
                            $mergeObjects: [
                              {
                                pph: {
                                  $let: {
                                    vars: {
                                      card: {
                                        $first: {
                                          $filter: {
                                            input: mineCards,
                                            as: "card",
                                            cond: {
                                              $eq: ["$$card._id", "$$role.k"],
                                            },
                                          },
                                        },
                                      },
                                    },

                                    in: {
                                      $multiply: [
                                        "$$card.base_pph",
                                        {
                                          $divide: [
                                            {
                                              $subtract: [
                                                {
                                                  $pow: [
                                                    "$$card.profit_increase_rate",
                                                    "$$role.v.level",
                                                  ],
                                                },
                                                1,
                                              ],
                                            },
                                            {
                                              $subtract: [
                                                "$$card.profit_increase_rate",
                                                1,
                                              ],
                                            },
                                          ],
                                        },
                                      ],
                                    },
                                  },
                                },
                              },
                            ],
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
            {
              $addFields: {
                cards_data: { $arrayToObject: "$cards_data" },
              },
            },
            {
              $addFields: {
                total_pph: {
                  $cond: {
                    if: {
                      $gt: [{ $size: { $objectToArray: "$cards_data" } }, 0],
                    },
                    then: {
                      $reduce: {
                        input: { $objectToArray: "$cards_data" },
                        initialValue: 0,
                        in: { $add: ["$$value", "$$this.v.pph"] },
                      },
                    },
                    else: 0,
                  },
                },
              },
            },
            {
              $addFields: {
                status: {
                  $cond: {
                    if: {
                      $and: [
                        { $gte: ["$total_pph", 10000] }, // Check if user's total_pph is >= 10,000
                        { $gte: [{ $size: "$referrals" }, 1] }, // Check if user has at least one referral
                      ],
                    },
                    then: "active",
                    else: "in-active",
                  },
                },
              },
            },
            {
              $project: {
                tg_id: 1,
                is_premium: 1,
                in_game_name: {
                  $ifNull: [
                    "$in_game_name",
                    {
                      $trim: {
                        input: {
                          $concat: [
                            "$first_name",
                            {
                              $cond: [
                                { $ifNull: ["$last_name", false] },
                                " ",
                                "",
                              ],
                            },
                            { $ifNull: ["$last_name", ""] },
                          ],
                        },
                      },
                    },
                  ],
                },
                total_points: 1,
                total_pph: 1,
                status: 1,
                referrals: 1,
                availability_status: {
                  $cond: {
                    if: {
                      $and: [
                        { $eq: ["$availability_status", "offline"] },
                        "$is_deleted",
                      ],
                    },
                    then: "deactivated",
                    else: "$availability_status",
                  },
                },
              },
            },
          ],
        },
      },
      {
        $addFields: {
          friends_active: {
            $size: {
              $filter: {
                input: "$invited_friends",
                as: "friend",
                cond: {
                  $and: [
                    { $gte: ["$$friend.total_pph", 10000] }, // Calculate if friend's pph is >= 10,000
                    { $gt: [{ $size: "$$friend.referrals" }, 0] }, // Check if friend has at least one referral
                  ],
                },
              },
            },
          },
          friends_total: { $size: "$referrals" }, // Total friends
        },
      },
      {
        $addFields: {
          friends_count: {
            $concat: [
              { $toString: "$friends_active" },
              " active/",
              {
                $toString: {
                  $subtract: ["$friends_total", "$friends_active"],
                },
              },
              " in-active",
            ],
          },
        },
      },
      {
        $unset: ["_id", "invited_friends.referrals"], // Exclude specific fields
      },
      {
        $project: {
          tg_id: 1,
          total_points: 1,
          invited_friends: 1,
          friends_count: 1,
        },
      },
    ]).exec();

    if (!(invitedFriends && invitedFriends.length > 0)) {
      return raiseException(404, "User doesn't exist");
    }

    const { tg_id, ...data } = invitedFriends[0];

    return {
      message: "Invited friends fetched successfully",
      payload: { ...data },
    };
  }
);

const changeSettings = generateController(
  async (request, response, raiseException) => {
    const { key, value } = request.body;
    const { userId } = request.user;

    if (
      (key === "haptic_feedback" && typeof value !== "boolean") ||
      (key === "language" && typeof value !== "string")
    ) {
      return raiseException(406, "typof `value` is not valid");
    }

    const userData = await User.findOneAndUpdate(
      { _id: userId, is_deleted: false },
      { $set: { [key]: value } }
    );

    if (!userData) {
      return raiseException(404, "User doesn't exist");
    }

    return {
      message: "Settings updated successfully",
    };
  }
);

const closeApp = generateController(
  async (request, response, raiseException) => {
    const { time } = request.body;
    const { userId } = request.user;

    const result = await addBonusOnClose(userId, time, false);
    if (result === null) {
      return raiseException(500, "Failed to add bonus");
    }

    return {
      message: "Bonus added successfully",
    };
  }
);

const continueApp = generateController(
  async (request, response, raiseException) => {
    const { time } = request.body;
    const { userId } = request.user;

    const userData = await User.aggregate([
      {
        $match: { _id: new ObjectId(userId), is_deleted: false },
      },
      {
        $lookup: {
          from: User.collection.name,
          let: { user_tg_id: "$tg_id" },
          pipeline: [
            {
              $match: {
                $expr: { $in: ["$$user_tg_id", "$referrals.tg_id"] },
              },
            },
            {
              $project: {
                tg_id: 1,
              },
            },
          ],
          as: "referred_by",
        },
      },
      {
        $unwind: {
          path: "$referred_by",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $project: {
          tg_id: 1,
          total_points: 1,
          referrals: 1,
          // cards_levels: 1,
          cards_data: 1,
          close_time: 1,
          availability_status: 1,
          referred_by: "$referred_by.tg_id",
        },
      },
    ]).exec();

    if (!(userData && userData.length > 0)) {
      return raiseException(500, "An error occurred while continuing the app");
    }

    if (!userData[0].cards_data) {
      return raiseException(500, "Failed to add bonus");
    }

    const result = await addBonusOnOpen(userData[0], time);
    if (result === null) {
      return raiseException(500, "Failed to add bonus");
    }

    return {
      message: "Bonus added successfully",
    };
  }
);

const giveJqToReferral = generateController(
  async (request, response, raiseException) => {
    const { userId } = request.user;

    const mineCards = await Card.find(
      {},
      { base_pph: 1, profit_increase_rate: 1 }
    ).exec();

    if (!mineCards) {
      return raiseException(
        500,
        "An error occurred while giving JQ to referral"
      );
    }

    const userData = await User.findOne(
      { _id: userId, is_deleted: false },
      {
        tg_id: 1,
        cards_data: 1,
        referrals: 1,
      }
    );

    if (!userData) {
      return raiseException(
        500,
        "An error occurred while giving JQ to referral"
      );
    }

    if (!userData.cards_data) {
      return raiseException(500, "Failed to give JQ to referral");
    }

    if (!(userData.referrals.length > 0)) {
      return raiseException(500, "Failed to give JQ to referral");
    }

    let value = 0;

    if (mineCards && userData.cards_data) {
      for (let cardId of Object.keys(userData.cards_data)) {
        const card = mineCards.find((card) => card._id === cardId);
        if (card != undefined) {
          value +=
            card.base_pph *
            ((card.profit_increase_rate ** userData.cards_data[cardId].level -
              1) /
              (card.profit_increase_rate - 1));
        }
      }
    }

    if (value < 10000) {
      return raiseException(500, "Failed to give JQ to referral");
    }

    const refUser = await User.findOne(
      { "referrals.tg_id": userData.tg_id }, // Match the user in the referrals array
      { referrals: { $elemMatch: { tg_id: userData.tg_id } }, jargon_quest: 1 } // Retrieve only the matched referral
    );

    if (!refUser) {
      return raiseException(404, "User not found");
    }

    if (refUser.referrals[0].retrieved_jq_from_ref) {
      return raiseException(406, "Already retrieved");
    }

    refUser.jargon_quest += 50;
    refUser.referrals[0].retrieved_jq_from_ref = true;
    const result = await refUser.save();

    if (!result) {
      return raiseException(
        500,
        "An error occurred while giving JQ to referral"
      );
    }

    return {
      message: "JQ given to referral",
    };
  }
);

const retrieveJqMilestone = generateController(
  async (request, response, raiseException) => {
    const { userId } = request.user;

    const userData = await User.findOne(
      { _id: userId, is_deleted: false },
      {
        jargon_quest: 1,
        cards_data: 1,
      }
    );

    if (!userData) {
      return raiseException(
        500,
        "An error occurred while retrieving JQ milestone"
      );
    }

    if (!(userData?.cards_data.BSC?.level >= 20)) {
      return raiseException(500, "Failed to retrieve JQ");
    }

    userData.jargon_quest += 10;
    const result = await userData.save();

    if (!result) {
      return raiseException(
        500,
        "An error occurred while giving JQ to referral"
      );
    }

    const { jargon_quest } = result;

    return {
      message: "JQ given to referral",
      payload: { jargon_quest },
    };
  }
);

// const giveJqToReferrals = generateController(
//   async (request, response, raiseException) => {
//     // const { userId } = request.user;

//     const users = await User.find({}).exec();

//     const mineCards = await Card.find(
//       {},
//       { base_pph: 1, profit_increase_rate: 1 }
//     ).exec();

//     for (const userData of users) {
//       // const userData = await User.findOne(
//       //   { _id: userId, is_deleted: false },
//       //   {
//       //     tg_id: 1,
//       //     cards_data: 1,
//       //     referrals: 1,
//       //   }
//       // );

//       if (!userData.cards_data) {
//         continue;
//       }

//       if (!(userData.referrals.length > 0)) {
//         continue;
//       }

//       let value = 0;

//       if (mineCards && userData.cards_data) {
//         for (let cardId of Object.keys(userData.cards_data)) {
//           const card = mineCards.find((card) => card._id === cardId);
//           if (card != undefined) {
//             value +=
//               card.base_pph *
//               ((card.profit_increase_rate ** userData.cards_data[cardId].level -
//                 1) /
//                 (card.profit_increase_rate - 1));
//           }
//         }
//       }

//       if (value < 10000) {
//         continue;
//       }

//       const refUser = await User.findOne(
//         { "referrals.tg_id": userData.tg_id }, // Match the user in the referrals array
//         {
//           referrals: { $elemMatch: { tg_id: userData.tg_id } },
//           jargon_quest: 1,
//         } // Retrieve only the matched referral
//       );

//       if (!refUser) {
//         continue;
//       }

//       if (refUser.referrals[0].retrieved_jq_from_ref) {
//         continue;
//       }

//       refUser.jargon_quest += 50;
//       refUser.referrals[0].retrieved_jq_from_ref = true;
//       const result = await refUser.save();

//       if (!result) {
//         return raiseException(406, "Failed to give JQ to referral");
//       }
//     }

//     return {
//       message: "JQ given to referral",
//     };
//   }
// );

// const updateBoosters = generateController(
//   async (request, response, raiseException) => {
//     const users = await User.find({ is_deleted: false });

//     const mineCards = await Card.find(
//       {},
//       { base_pph: 1, profit_increase_rate: 1 }
//     ).exec();

//     for (let user of users) {
//       // Base JQ reward for all users (P10)
//       user.jargon_quest += 100;

//       // Check if user has BSC card at level 20 or above
//       if (user.cards_data && user.cards_data["BSC"]?.level >= 20) {
//         let pph = 0;

//         if (mineCards && user.cards_data) {
//           for (let cardId of Object.keys(user.cards_data)) {
//             const card = mineCards.find((card) => card._id === cardId);
//             if (card != undefined) {
//               pph +=
//                 card.base_pph *
//                 ((card.profit_increase_rate ** user.cards_data[cardId].level -
//                   1) /
//                   (card.profit_increase_rate - 1));
//             }
//           }
//         }

//         // Calculate how many 100k PPH milestones the user has achieved
//         const milestones = Math.floor(pph / 100000);

//         // Award 10 JQ for each milestone
//         const milestonesReward = milestones * 10;

//         // Update user's JQ balance
//         user.jargon_quest += milestonesReward + 500;
//       }

//       const result = await user.save();
//       if (!result) {
//         return raiseException(406, "Failed to give JQ to referral");
//       }
//     }

//     return {
//       message: "Boosters and PPH milestones updated successfully",
//     };
//   }
// );

export {
  getGameData,
  getWebData,
  updatePoints,
  updateWalletAddress,
  updateInGameName,
  updateExchange,
  updateCountry,
  cardPurchase,
  SocialPointsPurchase,
  SocialPointsScratcher,
  affiliateProductInteraction,
  socialCardInteraction,
  boosterPurchase,
  getTopRankedUsers,
  getInvitedFriends,
  changeSettings,
  giveJqToReferral,
  retrieveJqMilestone,
  convertSocialPoints,
  claimJqPoints,
  jqPointsClaimed,
  FreezeSocialPoints,
  UnfreezeSocialPoints,
  closeApp,
  continueApp,
  // updateBoosters,
};
