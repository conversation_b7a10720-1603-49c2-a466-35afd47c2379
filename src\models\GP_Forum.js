import mongoose from "mongoose";

// User schema for sender information
const userSchema = new mongoose.Schema({
  user_id: { type: String, required: true },
  username: { type: String, required: true },
  level: { type: Number, default: 1 },
  avatar: { type: String, default: "" },
  is_admin: { type: Boolean, default: false },
  is_moderator: { type: Boolean, default: false },
});

// Reaction schema for message interactions
const reactionSchema = new mongoose.Schema({
  emoji: { type: String, required: true },
  count: { type: Number, default: 1 },
  users: [{ type: String }], // Array of user IDs who reacted
});

// Enhanced message schema
const messageSchema = new mongoose.Schema({
  message_id: { type: String, required: true, unique: true },
  sender: { type: userSchema, required: true },
  content: { type: String, required: true },
  message_type: {
    type: String,
    enum: ["text", "image", "file", "system"],
    default: "text"
  },
  reactions: [reactionSchema],
  reply_to: { type: String, default: null }, // Reference to another message ID
  // status: {
  //   type: String,
  //   enum: ["sent", "delivered", "read", "deleted"],
  //   default: "sent",
  // },
  created_at: { type: Date, default: Date.now },
});

// Enhanced chat/channel schema
const chatSchema = new mongoose.Schema({
  channel_id: { type: String, required: true, unique: true },
  channel_name: { type: String, required: true },
  channel_type: {
    type: String,
    enum: ["text", "voice", "announcement", "gaming", "trading", "support"],
    default: "text"
  },
  
  // Permission settings
  only_admin: { type: Boolean, default: false },
  only_moderators: { type: Boolean, default: false },
  
  // Channel stats
  member_count: { type: Number, default: 0 },
  online_count: { type: Number, default: 0 },
  
  // Messages
  messages: {
    type: [messageSchema],
    validate: {
      validator: function(v) {
        return Array.isArray(v);
      },
      message: 'Messages must be an array'
    }
  },
  
  // Pinned messages
  pinned_messages: [{ type: String }], // Array of message IDs
  
});

// Method to check if user can send messages
chatSchema.methods.canUserSendMessage = function(user) {
  if (this.only_admin && !user.is_admin) return false;
  if (this.only_moderators && !user.is_moderator && !user.is_admin) return false;
  return true;
};

// Method to add a message with permission check
chatSchema.methods.addMessage = function(messageData, user) {
  if (!this.canUserSendMessage(user)) {
    throw new Error('User does not have permission to send messages in this channel');
  }
  
  const message = {
    message_id: new mongoose.Types.ObjectId().toString(),
    sender: user,
    content: messageData.content,
    message_type: messageData.message_type || 'text',
    reply_to: messageData.reply_to || null,
  };
  
  this.messages.push(message);
  return message;
};

export const GP_Chat = mongoose.model("GP_Chat", chatSchema);
export const GP_Message = mongoose.model("GP_Message", messageSchema);
// export const GP_User = mongoose.model("GP_User", userSchema);